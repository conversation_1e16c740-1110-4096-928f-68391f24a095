{"ast": null, "code": "var _jsxFileName = \"C:\\\\Projeler\\\\kidgarden\\\\burky_root_web\\\\client\\\\src\\\\pages\\\\meeting\\\\MeetingPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { MicrophoneIcon, VideoCameraIcon, PhoneXMarkIcon, SpeakerWaveIcon } from '@heroicons/react/24/outline';\nimport { MicrophoneIcon as MicrophoneIconSolid, VideoCameraIcon as VideoCameraIconSolid, SpeakerWaveIcon as SpeakerWaveIconSolid } from '@heroicons/react/24/solid';\nimport io from 'socket.io-client';\nimport toast from 'react-hot-toast';\nimport { useAuth } from '../../hooks/useAuth';\n\n/**\n * P2P WebRTC Meeting Sayfası\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MeetingPage = () => {\n  _s();\n  const {\n    appointmentId\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    user,\n    hasPermission\n  } = useAuth();\n\n  // Permission check is now handled by PagePermission component\n  useEffect(() => {\n    var _user$role;\n    console.log('🔍 MeetingPage initialized for appointment:', appointmentId);\n    console.log('👤 User:', user ? {\n      id: user.id,\n      role: (_user$role = user.role) === null || _user$role === void 0 ? void 0 : _user$role.name\n    } : 'Not logged in');\n  }, [user, appointmentId]);\n\n  // State\n  const [isConnected, setIsConnected] = useState(false);\n  const [isAudioMuted, setIsAudioMuted] = useState(false);\n  const [isVideoMuted, setIsVideoMuted] = useState(false);\n  const [isSpeakerOn, setIsSpeakerOn] = useState(true);\n  const [connectionStatus, setConnectionStatus] = useState('Bağlanıyor...');\n  const [participantCount, setParticipantCount] = useState(0);\n\n  // Refs\n  const localVideoRef = useRef(null);\n  const remoteVideoRef = useRef(null);\n  const socketRef = useRef(null);\n  const peerConnectionRef = useRef(null);\n  const localStreamRef = useRef(null);\n\n  // WebRTC Configuration\n  const rtcConfig = {\n    iceServers: [{\n      urls: 'stun:stun.l.google.com:19302'\n    }, {\n      urls: 'stun:stun1.l.google.com:19302'\n    }]\n  };\n  useEffect(() => {\n    initializeConnection();\n    return () => {\n      cleanup();\n    };\n  }, [appointmentId]);\n  const initializeConnection = async () => {\n    try {\n      // Socket.IO bağlantısı - diğer sayfalarla tutarlı URL kullan\n      const socketUrl = process.env.REACT_APP_API_URL || 'https://*************:5000';\n      console.log('🔌 MEETING: Socket.IO bağlantısı deneniyor:', socketUrl);\n\n      // Token'ı al (authentication için)\n      const token = localStorage.getItem('accessToken');\n      socketRef.current = io(socketUrl, {\n        auth: {\n          token\n        },\n        transports: ['websocket', 'polling'],\n        timeout: 10000,\n        reconnection: true,\n        reconnectionDelay: 1000,\n        reconnectionAttempts: 5,\n        forceNew: false // Mevcut bağlantıyı yeniden kullan\n      });\n\n      // Socket event listeners\n      setupSocketListeners();\n\n      // Kullanıcı medyasını al\n      await getUserMedia();\n\n      // Odaya katıl\n      const roomId = `appointment-${appointmentId}`;\n      socketRef.current.emit('join', roomId);\n    } catch (error) {\n      console.error('Bağlantı hatası:', error);\n      toast.error('Bağlantı kurulamadı');\n      setConnectionStatus('Bağlantı hatası');\n    }\n  };\n  const setupSocketListeners = () => {\n    const socket = socketRef.current;\n\n    // Connection events\n    socket.on('connect', () => {\n      console.log('✅ MEETING: Socket.IO bağlantısı kuruldu:', socket.id);\n      setConnectionStatus('Bağlandı, odaya katılıyor...');\n    });\n    socket.on('disconnect', reason => {\n      console.log('❌ MEETING: Socket.IO bağlantısı kesildi:', reason);\n      setConnectionStatus('Bağlantı kesildi');\n      setIsConnected(false);\n    });\n    socket.on('connect_error', error => {\n      console.error('🔥 MEETING: Socket.IO bağlantı hatası:', error);\n      setConnectionStatus('Bağlantı hatası');\n    });\n    socket.on('joined', ({\n      roomId,\n      roomSize\n    }) => {\n      console.log(`✅ MEETING: Odaya katıldı: ${roomId}, Katılımcı sayısı: ${roomSize}`);\n      setParticipantCount(roomSize);\n      setConnectionStatus(`Odada ${roomSize} kişi`);\n      if (roomSize === 1) {\n        setConnectionStatus('Karşı tarafı bekleniyor...');\n      }\n    });\n    socket.on('ready', () => {\n      console.log('Oda hazır, WebRTC bağlantısı başlatılıyor');\n      setConnectionStatus('Bağlantı kuruluyor...');\n      createPeerConnection();\n      createOffer();\n    });\n    socket.on('full', roomId => {\n      console.log('Oda dolu:', roomId);\n      toast.error('Görüşme odası dolu');\n      setConnectionStatus('Oda dolu');\n    });\n    socket.on('offer', async sdp => {\n      console.log('Offer alındı');\n      await handleOffer(sdp);\n    });\n    socket.on('answer', async sdp => {\n      console.log('Answer alındı');\n      await handleAnswer(sdp);\n    });\n    socket.on('candidate', async candidate => {\n      console.log('ICE candidate alındı');\n      await handleCandidate(candidate);\n    });\n    socket.on('leave', () => {\n      console.log('Karşı taraf ayrıldı');\n      toast.info('Karşı taraf görüşmeden ayrıldı');\n      setConnectionStatus('Karşı taraf ayrıldı');\n      setParticipantCount(1);\n\n      // Remote video'yu temizle\n      if (remoteVideoRef.current) {\n        remoteVideoRef.current.srcObject = null;\n      }\n    });\n  };\n  const getUserMedia = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: true,\n        audio: true\n      });\n      localStreamRef.current = stream;\n      if (localVideoRef.current) {\n        localVideoRef.current.srcObject = stream;\n      }\n      console.log('Kullanıcı medyası alındı');\n    } catch (error) {\n      console.error('Medya erişim hatası:', error);\n      toast.error('Kamera ve mikrofon erişimi gerekli');\n    }\n  };\n  const createPeerConnection = () => {\n    const pc = new RTCPeerConnection(rtcConfig);\n    peerConnectionRef.current = pc;\n\n    // ICE candidate event\n    pc.onicecandidate = event => {\n      if (event.candidate) {\n        socketRef.current.emit('candidate', {\n          roomId: `appointment-${appointmentId}`,\n          candidate: event.candidate\n        });\n      }\n    };\n\n    // Remote stream event\n    pc.ontrack = event => {\n      console.log('Remote stream alındı');\n      const [remoteStream] = event.streams;\n      if (remoteVideoRef.current) {\n        remoteVideoRef.current.srcObject = remoteStream;\n      }\n      setIsConnected(true);\n      setConnectionStatus('Bağlantı kuruldu');\n    };\n\n    // Connection state change\n    pc.onconnectionstatechange = () => {\n      console.log('Connection state:', pc.connectionState);\n      if (pc.connectionState === 'connected') {\n        setIsConnected(true);\n        setConnectionStatus('Bağlantı kuruldu');\n      } else if (pc.connectionState === 'disconnected') {\n        setIsConnected(false);\n        setConnectionStatus('Bağlantı kesildi');\n      }\n    };\n\n    // Local stream'i peer connection'a ekle\n    if (localStreamRef.current) {\n      localStreamRef.current.getTracks().forEach(track => {\n        pc.addTrack(track, localStreamRef.current);\n      });\n    }\n  };\n  const createOffer = async () => {\n    try {\n      const pc = peerConnectionRef.current;\n      const offer = await pc.createOffer();\n      await pc.setLocalDescription(offer);\n      socketRef.current.emit('offer', {\n        roomId: `appointment-${appointmentId}`,\n        sdp: offer\n      });\n    } catch (error) {\n      console.error('Offer oluşturma hatası:', error);\n    }\n  };\n  const handleOffer = async sdp => {\n    try {\n      createPeerConnection();\n      const pc = peerConnectionRef.current;\n      await pc.setRemoteDescription(sdp);\n      const answer = await pc.createAnswer();\n      await pc.setLocalDescription(answer);\n      socketRef.current.emit('answer', {\n        roomId: `appointment-${appointmentId}`,\n        sdp: answer\n      });\n    } catch (error) {\n      console.error('Offer işleme hatası:', error);\n    }\n  };\n  const handleAnswer = async sdp => {\n    try {\n      const pc = peerConnectionRef.current;\n      await pc.setRemoteDescription(sdp);\n    } catch (error) {\n      console.error('Answer işleme hatası:', error);\n    }\n  };\n  const handleCandidate = async candidate => {\n    try {\n      const pc = peerConnectionRef.current;\n      if (pc && pc.remoteDescription) {\n        await pc.addIceCandidate(candidate);\n      }\n    } catch (error) {\n      console.error('ICE candidate işleme hatası:', error);\n    }\n  };\n  const toggleAudio = () => {\n    if (localStreamRef.current) {\n      const audioTrack = localStreamRef.current.getAudioTracks()[0];\n      if (audioTrack) {\n        audioTrack.enabled = !audioTrack.enabled;\n        setIsAudioMuted(!audioTrack.enabled);\n      }\n    }\n  };\n  const toggleVideo = () => {\n    if (localStreamRef.current) {\n      const videoTrack = localStreamRef.current.getVideoTracks()[0];\n      if (videoTrack) {\n        videoTrack.enabled = !videoTrack.enabled;\n        setIsVideoMuted(!videoTrack.enabled);\n      }\n    }\n  };\n  const toggleSpeaker = () => {\n    if (remoteVideoRef.current) {\n      remoteVideoRef.current.muted = isSpeakerOn;\n      setIsSpeakerOn(!isSpeakerOn);\n    }\n  };\n  const endCall = () => {\n    var _user$role2, _user$role3, _user$role4;\n    cleanup();\n\n    // Kullanıcının rolüne göre doğru sayfaya yönlendir\n    if (((_user$role2 = user.role) === null || _user$role2 === void 0 ? void 0 : _user$role2.name) === 'Admin') {\n      navigate('/admin/dashboard');\n    } else if (((_user$role3 = user.role) === null || _user$role3 === void 0 ? void 0 : _user$role3.name) === 'Expert') {\n      navigate('/expert/sessions');\n    } else if (((_user$role4 = user.role) === null || _user$role4 === void 0 ? void 0 : _user$role4.name) === 'Client') {\n      navigate('/client/sessions');\n    } else {\n      navigate('/');\n    }\n    toast.success('Görüşme sonlandırıldı');\n  };\n  const cleanup = () => {\n    console.log('🧹 MEETING: Cleanup başlatılıyor...');\n\n    // Socket bağlantısını kapat\n    if (socketRef.current) {\n      console.log('🔌 MEETING: Socket bağlantısı kapatılıyor...');\n      socketRef.current.emit('leave', `appointment-${appointmentId}`);\n      socketRef.current.off(); // Tüm event listener'ları kaldır\n      socketRef.current.disconnect();\n      socketRef.current = null;\n    }\n\n    // Peer connection'ı kapat\n    if (peerConnectionRef.current) {\n      console.log('🔗 MEETING: Peer connection kapatılıyor...');\n      peerConnectionRef.current.onicecandidate = null;\n      peerConnectionRef.current.ontrack = null;\n      peerConnectionRef.current.close();\n      peerConnectionRef.current = null;\n    }\n\n    // Local stream'i durdur\n    if (localStreamRef.current) {\n      console.log('📹 MEETING: Local stream durduruluyor...');\n      localStreamRef.current.getTracks().forEach(track => {\n        track.stop();\n        console.log(`📹 MEETING: Track durduruldu: ${track.kind}`);\n      });\n      localStreamRef.current = null;\n    }\n\n    // Video elementlerini temizle\n    if (localVideoRef.current) {\n      localVideoRef.current.srcObject = null;\n    }\n    if (remoteVideoRef.current) {\n      remoteVideoRef.current.srcObject = null;\n    }\n\n    // State'leri sıfırla\n    setIsConnected(false);\n    setParticipantCount(0);\n    setConnectionStatus('Bağlantı kesildi');\n    console.log('✅ MEETING: Cleanup tamamlandı');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-900 text-white\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 px-6 py-4 border-b border-gray-700\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-xl font-semibold\",\n            children: \"Psikolojik Dan\\u0131\\u015Fmanl\\u0131k Seans\\u0131\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-400\",\n            children: [\"Randevu #\", appointmentId]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-right\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-400\",\n            children: connectionStatus\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-500\",\n            children: [participantCount, \" kat\\u0131l\\u0131mc\\u0131\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 h-[calc(100vh-200px)]\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative bg-gray-800 rounded-lg overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"video\", {\n            ref: localVideoRef,\n            autoPlay: true,\n            playsInline: true,\n            muted: true,\n            className: \"w-full h-full object-cover\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-4 left-4 bg-black bg-opacity-50 px-3 py-1 rounded\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm\",\n              children: \"Siz\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 13\n          }, this), isVideoMuted && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gray-700 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(VideoCameraIconSolid, {\n              className: \"h-16 w-16 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative bg-gray-800 rounded-lg overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"video\", {\n            ref: remoteVideoRef,\n            autoPlay: true,\n            playsInline: true,\n            className: \"w-full h-full object-cover\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-4 left-4 bg-black bg-opacity-50 px-3 py-1 rounded\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm\",\n              children: \"Kar\\u015F\\u0131 Taraf\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this), !isConnected && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gray-700 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(VideoCameraIconSolid, {\n                className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-400\",\n                children: \"Kar\\u015F\\u0131 taraf bekleniyor...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-6 left-1/2 transform -translate-x-1/2\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-4 bg-gray-800 rounded-full px-6 py-3 shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: toggleAudio,\n          className: `p-3 rounded-full transition-colors ${isAudioMuted ? 'bg-red-600 hover:bg-red-700' : 'bg-gray-600 hover:bg-gray-700'}`,\n          children: isAudioMuted ? /*#__PURE__*/_jsxDEV(MicrophoneIconSolid, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(MicrophoneIcon, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: toggleVideo,\n          className: `p-3 rounded-full transition-colors ${isVideoMuted ? 'bg-red-600 hover:bg-red-700' : 'bg-gray-600 hover:bg-gray-700'}`,\n          children: isVideoMuted ? /*#__PURE__*/_jsxDEV(VideoCameraIconSolid, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(VideoCameraIcon, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: toggleSpeaker,\n          className: `p-3 rounded-full transition-colors ${!isSpeakerOn ? 'bg-red-600 hover:bg-red-700' : 'bg-gray-600 hover:bg-gray-700'}`,\n          children: isSpeakerOn ? /*#__PURE__*/_jsxDEV(SpeakerWaveIcon, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(SpeakerWaveIconSolid, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 475,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: endCall,\n          className: \"p-3 rounded-full bg-red-600 hover:bg-red-700 transition-colors\",\n          children: /*#__PURE__*/_jsxDEV(PhoneXMarkIcon, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 441,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 440,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 379,\n    columnNumber: 5\n  }, this);\n};\n_s(MeetingPage, \"3OhGwEnoHcI8RCfJhBa+RslAZ20=\", false, function () {\n  return [useParams, useNavigate, useAuth];\n});\n_c = MeetingPage;\nexport default MeetingPage;\nvar _c;\n$RefreshReg$(_c, \"MeetingPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useParams", "useNavigate", "MicrophoneIcon", "VideoCameraIcon", "PhoneXMarkIcon", "SpeakerWaveIcon", "MicrophoneIconSolid", "VideoCameraIconSolid", "SpeakerWaveIconSolid", "io", "toast", "useAuth", "jsxDEV", "_jsxDEV", "MeetingPage", "_s", "appointmentId", "navigate", "user", "hasPermission", "_user$role", "console", "log", "id", "role", "name", "isConnected", "setIsConnected", "isAudioMuted", "setIsAudioMuted", "isVideoMuted", "setIsVideoMuted", "isSpeakerOn", "setIsSpeakerOn", "connectionStatus", "setConnectionStatus", "participantCount", "setParticipantCount", "localVideoRef", "remoteVideoRef", "socketRef", "peerConnectionRef", "localStreamRef", "rtcConfig", "iceServers", "urls", "initializeConnection", "cleanup", "socketUrl", "process", "env", "REACT_APP_API_URL", "token", "localStorage", "getItem", "current", "auth", "transports", "timeout", "reconnection", "reconnectionDelay", "reconnectionAttempts", "forceNew", "setupSocketListeners", "getUserMedia", "roomId", "emit", "error", "socket", "on", "reason", "roomSize", "createPeerConnection", "createOffer", "sdp", "handleOffer", "handleAnswer", "candidate", "handleCandidate", "info", "srcObject", "stream", "navigator", "mediaDevices", "video", "audio", "pc", "RTCPeerConnection", "onicecandidate", "event", "ontrack", "remoteStream", "streams", "onconnectionstatechange", "connectionState", "getTracks", "for<PERSON>ach", "track", "addTrack", "offer", "setLocalDescription", "setRemoteDescription", "answer", "createAnswer", "remoteDescription", "addIceCandidate", "toggleAudio", "audioTrack", "getAudioTracks", "enabled", "toggleVideo", "videoTrack", "getVideoTracks", "toggleSpeaker", "muted", "endCall", "_user$role2", "_user$role3", "_user$role4", "success", "off", "disconnect", "close", "stop", "kind", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "autoPlay", "playsInline", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Projeler/kidgarden/burky_root_web/client/src/pages/meeting/MeetingPage.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport {\n  MicrophoneIcon,\n  VideoCameraIcon,\n  PhoneXMarkIcon,\n  SpeakerWaveIcon\n} from '@heroicons/react/24/outline';\nimport {\n  MicrophoneIcon as MicrophoneIconSolid,\n  VideoCameraIcon as VideoCameraIconSolid,\n  SpeakerWaveIcon as SpeakerWaveIconSolid\n} from '@heroicons/react/24/solid';\nimport io from 'socket.io-client';\nimport toast from 'react-hot-toast';\nimport { useAuth } from '../../hooks/useAuth';\n\n/**\n * P2P WebRTC Meeting Sayfası\n */\nconst MeetingPage = () => {\n  const { appointmentId } = useParams();\n  const navigate = useNavigate();\n  const { user, hasPermission } = useAuth();\n\n  // Permission check is now handled by PagePermission component\n  useEffect(() => {\n    console.log('🔍 MeetingPage initialized for appointment:', appointmentId);\n    console.log('👤 User:', user ? { id: user.id, role: user.role?.name } : 'Not logged in');\n  }, [user, appointmentId]);\n  \n  // State\n  const [isConnected, setIsConnected] = useState(false);\n  const [isAudioMuted, setIsAudioMuted] = useState(false);\n  const [isVideoMuted, setIsVideoMuted] = useState(false);\n  const [isSpeakerOn, setIsSpeakerOn] = useState(true);\n  const [connectionStatus, setConnectionStatus] = useState('Bağlanıyor...');\n  const [participantCount, setParticipantCount] = useState(0);\n  \n  // Refs\n  const localVideoRef = useRef(null);\n  const remoteVideoRef = useRef(null);\n  const socketRef = useRef(null);\n  const peerConnectionRef = useRef(null);\n  const localStreamRef = useRef(null);\n  \n  // WebRTC Configuration\n  const rtcConfig = {\n    iceServers: [\n      { urls: 'stun:stun.l.google.com:19302' },\n      { urls: 'stun:stun1.l.google.com:19302' },\n    ],\n  };\n\n  useEffect(() => {\n    initializeConnection();\n    \n    return () => {\n      cleanup();\n    };\n  }, [appointmentId]);\n\n  const initializeConnection = async () => {\n    try {\n      // Socket.IO bağlantısı - diğer sayfalarla tutarlı URL kullan\n      const socketUrl = process.env.REACT_APP_API_URL || 'https://*************:5000';\n      console.log('🔌 MEETING: Socket.IO bağlantısı deneniyor:', socketUrl);\n\n      // Token'ı al (authentication için)\n      const token = localStorage.getItem('accessToken');\n\n      socketRef.current = io(socketUrl, {\n        auth: { token },\n        transports: ['websocket', 'polling'],\n        timeout: 10000,\n        reconnection: true,\n        reconnectionDelay: 1000,\n        reconnectionAttempts: 5,\n        forceNew: false // Mevcut bağlantıyı yeniden kullan\n      });\n      \n      // Socket event listeners\n      setupSocketListeners();\n      \n      // Kullanıcı medyasını al\n      await getUserMedia();\n      \n      // Odaya katıl\n      const roomId = `appointment-${appointmentId}`;\n      socketRef.current.emit('join', roomId);\n      \n    } catch (error) {\n      console.error('Bağlantı hatası:', error);\n      toast.error('Bağlantı kurulamadı');\n      setConnectionStatus('Bağlantı hatası');\n    }\n  };\n\n  const setupSocketListeners = () => {\n    const socket = socketRef.current;\n\n    // Connection events\n    socket.on('connect', () => {\n      console.log('✅ MEETING: Socket.IO bağlantısı kuruldu:', socket.id);\n      setConnectionStatus('Bağlandı, odaya katılıyor...');\n    });\n\n    socket.on('disconnect', (reason) => {\n      console.log('❌ MEETING: Socket.IO bağlantısı kesildi:', reason);\n      setConnectionStatus('Bağlantı kesildi');\n      setIsConnected(false);\n    });\n\n    socket.on('connect_error', (error) => {\n      console.error('🔥 MEETING: Socket.IO bağlantı hatası:', error);\n      setConnectionStatus('Bağlantı hatası');\n    });\n\n    socket.on('joined', ({ roomId, roomSize }) => {\n      console.log(`✅ MEETING: Odaya katıldı: ${roomId}, Katılımcı sayısı: ${roomSize}`);\n      setParticipantCount(roomSize);\n      setConnectionStatus(`Odada ${roomSize} kişi`);\n\n      if (roomSize === 1) {\n        setConnectionStatus('Karşı tarafı bekleniyor...');\n      }\n    });\n\n    socket.on('ready', () => {\n      console.log('Oda hazır, WebRTC bağlantısı başlatılıyor');\n      setConnectionStatus('Bağlantı kuruluyor...');\n      createPeerConnection();\n      createOffer();\n    });\n\n    socket.on('full', (roomId) => {\n      console.log('Oda dolu:', roomId);\n      toast.error('Görüşme odası dolu');\n      setConnectionStatus('Oda dolu');\n    });\n\n    socket.on('offer', async (sdp) => {\n      console.log('Offer alındı');\n      await handleOffer(sdp);\n    });\n\n    socket.on('answer', async (sdp) => {\n      console.log('Answer alındı');\n      await handleAnswer(sdp);\n    });\n\n    socket.on('candidate', async (candidate) => {\n      console.log('ICE candidate alındı');\n      await handleCandidate(candidate);\n    });\n\n    socket.on('leave', () => {\n      console.log('Karşı taraf ayrıldı');\n      toast.info('Karşı taraf görüşmeden ayrıldı');\n      setConnectionStatus('Karşı taraf ayrıldı');\n      setParticipantCount(1);\n      \n      // Remote video'yu temizle\n      if (remoteVideoRef.current) {\n        remoteVideoRef.current.srcObject = null;\n      }\n    });\n  };\n\n  const getUserMedia = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({ \n        video: true, \n        audio: true \n      });\n      \n      localStreamRef.current = stream;\n      if (localVideoRef.current) {\n        localVideoRef.current.srcObject = stream;\n      }\n      \n      console.log('Kullanıcı medyası alındı');\n    } catch (error) {\n      console.error('Medya erişim hatası:', error);\n      toast.error('Kamera ve mikrofon erişimi gerekli');\n    }\n  };\n\n  const createPeerConnection = () => {\n    const pc = new RTCPeerConnection(rtcConfig);\n    peerConnectionRef.current = pc;\n\n    // ICE candidate event\n    pc.onicecandidate = (event) => {\n      if (event.candidate) {\n        socketRef.current.emit('candidate', {\n          roomId: `appointment-${appointmentId}`,\n          candidate: event.candidate\n        });\n      }\n    };\n\n    // Remote stream event\n    pc.ontrack = (event) => {\n      console.log('Remote stream alındı');\n      const [remoteStream] = event.streams;\n      if (remoteVideoRef.current) {\n        remoteVideoRef.current.srcObject = remoteStream;\n      }\n      setIsConnected(true);\n      setConnectionStatus('Bağlantı kuruldu');\n    };\n\n    // Connection state change\n    pc.onconnectionstatechange = () => {\n      console.log('Connection state:', pc.connectionState);\n      if (pc.connectionState === 'connected') {\n        setIsConnected(true);\n        setConnectionStatus('Bağlantı kuruldu');\n      } else if (pc.connectionState === 'disconnected') {\n        setIsConnected(false);\n        setConnectionStatus('Bağlantı kesildi');\n      }\n    };\n\n    // Local stream'i peer connection'a ekle\n    if (localStreamRef.current) {\n      localStreamRef.current.getTracks().forEach(track => {\n        pc.addTrack(track, localStreamRef.current);\n      });\n    }\n  };\n\n  const createOffer = async () => {\n    try {\n      const pc = peerConnectionRef.current;\n      const offer = await pc.createOffer();\n      await pc.setLocalDescription(offer);\n      \n      socketRef.current.emit('offer', {\n        roomId: `appointment-${appointmentId}`,\n        sdp: offer\n      });\n    } catch (error) {\n      console.error('Offer oluşturma hatası:', error);\n    }\n  };\n\n  const handleOffer = async (sdp) => {\n    try {\n      createPeerConnection();\n      const pc = peerConnectionRef.current;\n      \n      await pc.setRemoteDescription(sdp);\n      const answer = await pc.createAnswer();\n      await pc.setLocalDescription(answer);\n      \n      socketRef.current.emit('answer', {\n        roomId: `appointment-${appointmentId}`,\n        sdp: answer\n      });\n    } catch (error) {\n      console.error('Offer işleme hatası:', error);\n    }\n  };\n\n  const handleAnswer = async (sdp) => {\n    try {\n      const pc = peerConnectionRef.current;\n      await pc.setRemoteDescription(sdp);\n    } catch (error) {\n      console.error('Answer işleme hatası:', error);\n    }\n  };\n\n  const handleCandidate = async (candidate) => {\n    try {\n      const pc = peerConnectionRef.current;\n      if (pc && pc.remoteDescription) {\n        await pc.addIceCandidate(candidate);\n      }\n    } catch (error) {\n      console.error('ICE candidate işleme hatası:', error);\n    }\n  };\n\n  const toggleAudio = () => {\n    if (localStreamRef.current) {\n      const audioTrack = localStreamRef.current.getAudioTracks()[0];\n      if (audioTrack) {\n        audioTrack.enabled = !audioTrack.enabled;\n        setIsAudioMuted(!audioTrack.enabled);\n      }\n    }\n  };\n\n  const toggleVideo = () => {\n    if (localStreamRef.current) {\n      const videoTrack = localStreamRef.current.getVideoTracks()[0];\n      if (videoTrack) {\n        videoTrack.enabled = !videoTrack.enabled;\n        setIsVideoMuted(!videoTrack.enabled);\n      }\n    }\n  };\n\n  const toggleSpeaker = () => {\n    if (remoteVideoRef.current) {\n      remoteVideoRef.current.muted = isSpeakerOn;\n      setIsSpeakerOn(!isSpeakerOn);\n    }\n  };\n\n  const endCall = () => {\n    cleanup();\n\n    // Kullanıcının rolüne göre doğru sayfaya yönlendir\n    if (user.role?.name === 'Admin') {\n      navigate('/admin/dashboard');\n    } else if (user.role?.name === 'Expert') {\n      navigate('/expert/sessions');\n    } else if (user.role?.name === 'Client') {\n      navigate('/client/sessions');\n    } else {\n      navigate('/');\n    }\n\n    toast.success('Görüşme sonlandırıldı');\n  };\n\n  const cleanup = () => {\n    console.log('🧹 MEETING: Cleanup başlatılıyor...');\n\n    // Socket bağlantısını kapat\n    if (socketRef.current) {\n      console.log('🔌 MEETING: Socket bağlantısı kapatılıyor...');\n      socketRef.current.emit('leave', `appointment-${appointmentId}`);\n      socketRef.current.off(); // Tüm event listener'ları kaldır\n      socketRef.current.disconnect();\n      socketRef.current = null;\n    }\n\n    // Peer connection'ı kapat\n    if (peerConnectionRef.current) {\n      console.log('🔗 MEETING: Peer connection kapatılıyor...');\n      peerConnectionRef.current.onicecandidate = null;\n      peerConnectionRef.current.ontrack = null;\n      peerConnectionRef.current.close();\n      peerConnectionRef.current = null;\n    }\n\n    // Local stream'i durdur\n    if (localStreamRef.current) {\n      console.log('📹 MEETING: Local stream durduruluyor...');\n      localStreamRef.current.getTracks().forEach(track => {\n        track.stop();\n        console.log(`📹 MEETING: Track durduruldu: ${track.kind}`);\n      });\n      localStreamRef.current = null;\n    }\n\n    // Video elementlerini temizle\n    if (localVideoRef.current) {\n      localVideoRef.current.srcObject = null;\n    }\n    if (remoteVideoRef.current) {\n      remoteVideoRef.current.srcObject = null;\n    }\n\n    // State'leri sıfırla\n    setIsConnected(false);\n    setParticipantCount(0);\n    setConnectionStatus('Bağlantı kesildi');\n\n    console.log('✅ MEETING: Cleanup tamamlandı');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 text-white\">\n      {/* Header */}\n      <div className=\"bg-gray-800 px-6 py-4 border-b border-gray-700\">\n        <div className=\"flex justify-between items-center\">\n          <div>\n            <h1 className=\"text-xl font-semibold\">Psikolojik Danışmanlık Seansı</h1>\n            <p className=\"text-sm text-gray-400\">Randevu #{appointmentId}</p>\n          </div>\n          <div className=\"text-right\">\n            <p className=\"text-sm text-gray-400\">{connectionStatus}</p>\n            <p className=\"text-xs text-gray-500\">{participantCount} katılımcı</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Video Container */}\n      <div className=\"flex-1 p-6\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 h-[calc(100vh-200px)]\">\n          {/* Local Video */}\n          <div className=\"relative bg-gray-800 rounded-lg overflow-hidden\">\n            <video\n              ref={localVideoRef}\n              autoPlay\n              playsInline\n              muted\n              className=\"w-full h-full object-cover\"\n            />\n            <div className=\"absolute bottom-4 left-4 bg-black bg-opacity-50 px-3 py-1 rounded\">\n              <span className=\"text-sm\">Siz</span>\n            </div>\n            {isVideoMuted && (\n              <div className=\"absolute inset-0 bg-gray-700 flex items-center justify-center\">\n                <VideoCameraIconSolid className=\"h-16 w-16 text-gray-400\" />\n              </div>\n            )}\n          </div>\n\n          {/* Remote Video */}\n          <div className=\"relative bg-gray-800 rounded-lg overflow-hidden\">\n            <video\n              ref={remoteVideoRef}\n              autoPlay\n              playsInline\n              className=\"w-full h-full object-cover\"\n            />\n            <div className=\"absolute bottom-4 left-4 bg-black bg-opacity-50 px-3 py-1 rounded\">\n              <span className=\"text-sm\">Karşı Taraf</span>\n            </div>\n            {!isConnected && (\n              <div className=\"absolute inset-0 bg-gray-700 flex items-center justify-center\">\n                <div className=\"text-center\">\n                  <VideoCameraIconSolid className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n                  <p className=\"text-gray-400\">Karşı taraf bekleniyor...</p>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Controls */}\n      <div className=\"fixed bottom-6 left-1/2 transform -translate-x-1/2\">\n        <div className=\"flex space-x-4 bg-gray-800 rounded-full px-6 py-3 shadow-lg\">\n          {/* Mikrofon */}\n          <button\n            onClick={toggleAudio}\n            className={`p-3 rounded-full transition-colors ${\n              isAudioMuted \n                ? 'bg-red-600 hover:bg-red-700' \n                : 'bg-gray-600 hover:bg-gray-700'\n            }`}\n          >\n            {isAudioMuted ? (\n              <MicrophoneIconSolid className=\"h-6 w-6\" />\n            ) : (\n              <MicrophoneIcon className=\"h-6 w-6\" />\n            )}\n          </button>\n\n          {/* Kamera */}\n          <button\n            onClick={toggleVideo}\n            className={`p-3 rounded-full transition-colors ${\n              isVideoMuted \n                ? 'bg-red-600 hover:bg-red-700' \n                : 'bg-gray-600 hover:bg-gray-700'\n            }`}\n          >\n            {isVideoMuted ? (\n              <VideoCameraIconSolid className=\"h-6 w-6\" />\n            ) : (\n              <VideoCameraIcon className=\"h-6 w-6\" />\n            )}\n          </button>\n\n          {/* Hoparlör */}\n          <button\n            onClick={toggleSpeaker}\n            className={`p-3 rounded-full transition-colors ${\n              !isSpeakerOn \n                ? 'bg-red-600 hover:bg-red-700' \n                : 'bg-gray-600 hover:bg-gray-700'\n            }`}\n          >\n            {isSpeakerOn ? (\n              <SpeakerWaveIcon className=\"h-6 w-6\" />\n            ) : (\n              <SpeakerWaveIconSolid className=\"h-6 w-6\" />\n            )}\n          </button>\n\n          {/* Görüşmeyi Sonlandır */}\n          <button\n            onClick={endCall}\n            className=\"p-3 rounded-full bg-red-600 hover:bg-red-700 transition-colors\"\n          >\n            <PhoneXMarkIcon className=\"h-6 w-6\" />\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default MeetingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SACEC,cAAc,EACdC,eAAe,EACfC,cAAc,EACdC,eAAe,QACV,6BAA6B;AACpC,SACEH,cAAc,IAAII,mBAAmB,EACrCH,eAAe,IAAII,oBAAoB,EACvCF,eAAe,IAAIG,oBAAoB,QAClC,2BAA2B;AAClC,OAAOC,EAAE,MAAM,kBAAkB;AACjC,OAAOC,KAAK,MAAM,iBAAiB;AACnC,SAASC,OAAO,QAAQ,qBAAqB;;AAE7C;AACA;AACA;AAFA,SAAAC,MAAA,IAAAC,OAAA;AAGA,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC;EAAc,CAAC,GAAGhB,SAAS,CAAC,CAAC;EACrC,MAAMiB,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEiB,IAAI;IAAEC;EAAc,CAAC,GAAGR,OAAO,CAAC,CAAC;;EAEzC;EACAb,SAAS,CAAC,MAAM;IAAA,IAAAsB,UAAA;IACdC,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEN,aAAa,CAAC;IACzEK,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEJ,IAAI,GAAG;MAAEK,EAAE,EAAEL,IAAI,CAACK,EAAE;MAAEC,IAAI,GAAAJ,UAAA,GAAEF,IAAI,CAACM,IAAI,cAAAJ,UAAA,uBAATA,UAAA,CAAWK;IAAK,CAAC,GAAG,eAAe,CAAC;EAC1F,CAAC,EAAE,CAACP,IAAI,EAAEF,aAAa,CAAC,CAAC;;EAEzB;EACA,MAAM,CAACU,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACqC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtC,QAAQ,CAAC,eAAe,CAAC;EACzE,MAAM,CAACuC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxC,QAAQ,CAAC,CAAC,CAAC;;EAE3D;EACA,MAAMyC,aAAa,GAAGvC,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMwC,cAAc,GAAGxC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMyC,SAAS,GAAGzC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM0C,iBAAiB,GAAG1C,MAAM,CAAC,IAAI,CAAC;EACtC,MAAM2C,cAAc,GAAG3C,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAM4C,SAAS,GAAG;IAChBC,UAAU,EAAE,CACV;MAAEC,IAAI,EAAE;IAA+B,CAAC,EACxC;MAAEA,IAAI,EAAE;IAAgC,CAAC;EAE7C,CAAC;EAED/C,SAAS,CAAC,MAAM;IACdgD,oBAAoB,CAAC,CAAC;IAEtB,OAAO,MAAM;MACXC,OAAO,CAAC,CAAC;IACX,CAAC;EACH,CAAC,EAAE,CAAC/B,aAAa,CAAC,CAAC;EAEnB,MAAM8B,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF;MACA,MAAME,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,4BAA4B;MAC/E9B,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE0B,SAAS,CAAC;;MAErE;MACA,MAAMI,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MAEjDd,SAAS,CAACe,OAAO,GAAG9C,EAAE,CAACuC,SAAS,EAAE;QAChCQ,IAAI,EAAE;UAAEJ;QAAM,CAAC;QACfK,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;QACpCC,OAAO,EAAE,KAAK;QACdC,YAAY,EAAE,IAAI;QAClBC,iBAAiB,EAAE,IAAI;QACvBC,oBAAoB,EAAE,CAAC;QACvBC,QAAQ,EAAE,KAAK,CAAC;MAClB,CAAC,CAAC;;MAEF;MACAC,oBAAoB,CAAC,CAAC;;MAEtB;MACA,MAAMC,YAAY,CAAC,CAAC;;MAEpB;MACA,MAAMC,MAAM,GAAG,eAAejD,aAAa,EAAE;MAC7CwB,SAAS,CAACe,OAAO,CAACW,IAAI,CAAC,MAAM,EAAED,MAAM,CAAC;IAExC,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd9C,OAAO,CAAC8C,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxCzD,KAAK,CAACyD,KAAK,CAAC,qBAAqB,CAAC;MAClChC,mBAAmB,CAAC,iBAAiB,CAAC;IACxC;EACF,CAAC;EAED,MAAM4B,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMK,MAAM,GAAG5B,SAAS,CAACe,OAAO;;IAEhC;IACAa,MAAM,CAACC,EAAE,CAAC,SAAS,EAAE,MAAM;MACzBhD,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE8C,MAAM,CAAC7C,EAAE,CAAC;MAClEY,mBAAmB,CAAC,8BAA8B,CAAC;IACrD,CAAC,CAAC;IAEFiC,MAAM,CAACC,EAAE,CAAC,YAAY,EAAGC,MAAM,IAAK;MAClCjD,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEgD,MAAM,CAAC;MAC/DnC,mBAAmB,CAAC,kBAAkB,CAAC;MACvCR,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,CAAC;IAEFyC,MAAM,CAACC,EAAE,CAAC,eAAe,EAAGF,KAAK,IAAK;MACpC9C,OAAO,CAAC8C,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9DhC,mBAAmB,CAAC,iBAAiB,CAAC;IACxC,CAAC,CAAC;IAEFiC,MAAM,CAACC,EAAE,CAAC,QAAQ,EAAE,CAAC;MAAEJ,MAAM;MAAEM;IAAS,CAAC,KAAK;MAC5ClD,OAAO,CAACC,GAAG,CAAC,6BAA6B2C,MAAM,uBAAuBM,QAAQ,EAAE,CAAC;MACjFlC,mBAAmB,CAACkC,QAAQ,CAAC;MAC7BpC,mBAAmB,CAAC,SAASoC,QAAQ,OAAO,CAAC;MAE7C,IAAIA,QAAQ,KAAK,CAAC,EAAE;QAClBpC,mBAAmB,CAAC,4BAA4B,CAAC;MACnD;IACF,CAAC,CAAC;IAEFiC,MAAM,CAACC,EAAE,CAAC,OAAO,EAAE,MAAM;MACvBhD,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxDa,mBAAmB,CAAC,uBAAuB,CAAC;MAC5CqC,oBAAoB,CAAC,CAAC;MACtBC,WAAW,CAAC,CAAC;IACf,CAAC,CAAC;IAEFL,MAAM,CAACC,EAAE,CAAC,MAAM,EAAGJ,MAAM,IAAK;MAC5B5C,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE2C,MAAM,CAAC;MAChCvD,KAAK,CAACyD,KAAK,CAAC,oBAAoB,CAAC;MACjChC,mBAAmB,CAAC,UAAU,CAAC;IACjC,CAAC,CAAC;IAEFiC,MAAM,CAACC,EAAE,CAAC,OAAO,EAAE,MAAOK,GAAG,IAAK;MAChCrD,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;MAC3B,MAAMqD,WAAW,CAACD,GAAG,CAAC;IACxB,CAAC,CAAC;IAEFN,MAAM,CAACC,EAAE,CAAC,QAAQ,EAAE,MAAOK,GAAG,IAAK;MACjCrD,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B,MAAMsD,YAAY,CAACF,GAAG,CAAC;IACzB,CAAC,CAAC;IAEFN,MAAM,CAACC,EAAE,CAAC,WAAW,EAAE,MAAOQ,SAAS,IAAK;MAC1CxD,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnC,MAAMwD,eAAe,CAACD,SAAS,CAAC;IAClC,CAAC,CAAC;IAEFT,MAAM,CAACC,EAAE,CAAC,OAAO,EAAE,MAAM;MACvBhD,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MAClCZ,KAAK,CAACqE,IAAI,CAAC,gCAAgC,CAAC;MAC5C5C,mBAAmB,CAAC,qBAAqB,CAAC;MAC1CE,mBAAmB,CAAC,CAAC,CAAC;;MAEtB;MACA,IAAIE,cAAc,CAACgB,OAAO,EAAE;QAC1BhB,cAAc,CAACgB,OAAO,CAACyB,SAAS,GAAG,IAAI;MACzC;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMhB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMiB,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACnB,YAAY,CAAC;QACvDoB,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE;MACT,CAAC,CAAC;MAEF3C,cAAc,CAACa,OAAO,GAAG0B,MAAM;MAC/B,IAAI3C,aAAa,CAACiB,OAAO,EAAE;QACzBjB,aAAa,CAACiB,OAAO,CAACyB,SAAS,GAAGC,MAAM;MAC1C;MAEA5D,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACzC,CAAC,CAAC,OAAO6C,KAAK,EAAE;MACd9C,OAAO,CAAC8C,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CzD,KAAK,CAACyD,KAAK,CAAC,oCAAoC,CAAC;IACnD;EACF,CAAC;EAED,MAAMK,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMc,EAAE,GAAG,IAAIC,iBAAiB,CAAC5C,SAAS,CAAC;IAC3CF,iBAAiB,CAACc,OAAO,GAAG+B,EAAE;;IAE9B;IACAA,EAAE,CAACE,cAAc,GAAIC,KAAK,IAAK;MAC7B,IAAIA,KAAK,CAACZ,SAAS,EAAE;QACnBrC,SAAS,CAACe,OAAO,CAACW,IAAI,CAAC,WAAW,EAAE;UAClCD,MAAM,EAAE,eAAejD,aAAa,EAAE;UACtC6D,SAAS,EAAEY,KAAK,CAACZ;QACnB,CAAC,CAAC;MACJ;IACF,CAAC;;IAED;IACAS,EAAE,CAACI,OAAO,GAAID,KAAK,IAAK;MACtBpE,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnC,MAAM,CAACqE,YAAY,CAAC,GAAGF,KAAK,CAACG,OAAO;MACpC,IAAIrD,cAAc,CAACgB,OAAO,EAAE;QAC1BhB,cAAc,CAACgB,OAAO,CAACyB,SAAS,GAAGW,YAAY;MACjD;MACAhE,cAAc,CAAC,IAAI,CAAC;MACpBQ,mBAAmB,CAAC,kBAAkB,CAAC;IACzC,CAAC;;IAED;IACAmD,EAAE,CAACO,uBAAuB,GAAG,MAAM;MACjCxE,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEgE,EAAE,CAACQ,eAAe,CAAC;MACpD,IAAIR,EAAE,CAACQ,eAAe,KAAK,WAAW,EAAE;QACtCnE,cAAc,CAAC,IAAI,CAAC;QACpBQ,mBAAmB,CAAC,kBAAkB,CAAC;MACzC,CAAC,MAAM,IAAImD,EAAE,CAACQ,eAAe,KAAK,cAAc,EAAE;QAChDnE,cAAc,CAAC,KAAK,CAAC;QACrBQ,mBAAmB,CAAC,kBAAkB,CAAC;MACzC;IACF,CAAC;;IAED;IACA,IAAIO,cAAc,CAACa,OAAO,EAAE;MAC1Bb,cAAc,CAACa,OAAO,CAACwC,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAI;QAClDX,EAAE,CAACY,QAAQ,CAACD,KAAK,EAAEvD,cAAc,CAACa,OAAO,CAAC;MAC5C,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMkB,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMa,EAAE,GAAG7C,iBAAiB,CAACc,OAAO;MACpC,MAAM4C,KAAK,GAAG,MAAMb,EAAE,CAACb,WAAW,CAAC,CAAC;MACpC,MAAMa,EAAE,CAACc,mBAAmB,CAACD,KAAK,CAAC;MAEnC3D,SAAS,CAACe,OAAO,CAACW,IAAI,CAAC,OAAO,EAAE;QAC9BD,MAAM,EAAE,eAAejD,aAAa,EAAE;QACtC0D,GAAG,EAAEyB;MACP,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOhC,KAAK,EAAE;MACd9C,OAAO,CAAC8C,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAMQ,WAAW,GAAG,MAAOD,GAAG,IAAK;IACjC,IAAI;MACFF,oBAAoB,CAAC,CAAC;MACtB,MAAMc,EAAE,GAAG7C,iBAAiB,CAACc,OAAO;MAEpC,MAAM+B,EAAE,CAACe,oBAAoB,CAAC3B,GAAG,CAAC;MAClC,MAAM4B,MAAM,GAAG,MAAMhB,EAAE,CAACiB,YAAY,CAAC,CAAC;MACtC,MAAMjB,EAAE,CAACc,mBAAmB,CAACE,MAAM,CAAC;MAEpC9D,SAAS,CAACe,OAAO,CAACW,IAAI,CAAC,QAAQ,EAAE;QAC/BD,MAAM,EAAE,eAAejD,aAAa,EAAE;QACtC0D,GAAG,EAAE4B;MACP,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOnC,KAAK,EAAE;MACd9C,OAAO,CAAC8C,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;EACF,CAAC;EAED,MAAMS,YAAY,GAAG,MAAOF,GAAG,IAAK;IAClC,IAAI;MACF,MAAMY,EAAE,GAAG7C,iBAAiB,CAACc,OAAO;MACpC,MAAM+B,EAAE,CAACe,oBAAoB,CAAC3B,GAAG,CAAC;IACpC,CAAC,CAAC,OAAOP,KAAK,EAAE;MACd9C,OAAO,CAAC8C,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAMW,eAAe,GAAG,MAAOD,SAAS,IAAK;IAC3C,IAAI;MACF,MAAMS,EAAE,GAAG7C,iBAAiB,CAACc,OAAO;MACpC,IAAI+B,EAAE,IAAIA,EAAE,CAACkB,iBAAiB,EAAE;QAC9B,MAAMlB,EAAE,CAACmB,eAAe,CAAC5B,SAAS,CAAC;MACrC;IACF,CAAC,CAAC,OAAOV,KAAK,EAAE;MACd9C,OAAO,CAAC8C,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED,MAAMuC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIhE,cAAc,CAACa,OAAO,EAAE;MAC1B,MAAMoD,UAAU,GAAGjE,cAAc,CAACa,OAAO,CAACqD,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7D,IAAID,UAAU,EAAE;QACdA,UAAU,CAACE,OAAO,GAAG,CAACF,UAAU,CAACE,OAAO;QACxChF,eAAe,CAAC,CAAC8E,UAAU,CAACE,OAAO,CAAC;MACtC;IACF;EACF,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIpE,cAAc,CAACa,OAAO,EAAE;MAC1B,MAAMwD,UAAU,GAAGrE,cAAc,CAACa,OAAO,CAACyD,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7D,IAAID,UAAU,EAAE;QACdA,UAAU,CAACF,OAAO,GAAG,CAACE,UAAU,CAACF,OAAO;QACxC9E,eAAe,CAAC,CAACgF,UAAU,CAACF,OAAO,CAAC;MACtC;IACF;EACF,CAAC;EAED,MAAMI,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI1E,cAAc,CAACgB,OAAO,EAAE;MAC1BhB,cAAc,CAACgB,OAAO,CAAC2D,KAAK,GAAGlF,WAAW;MAC1CC,cAAc,CAAC,CAACD,WAAW,CAAC;IAC9B;EACF,CAAC;EAED,MAAMmF,OAAO,GAAGA,CAAA,KAAM;IAAA,IAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA;IACpBvE,OAAO,CAAC,CAAC;;IAET;IACA,IAAI,EAAAqE,WAAA,GAAAlG,IAAI,CAACM,IAAI,cAAA4F,WAAA,uBAATA,WAAA,CAAW3F,IAAI,MAAK,OAAO,EAAE;MAC/BR,QAAQ,CAAC,kBAAkB,CAAC;IAC9B,CAAC,MAAM,IAAI,EAAAoG,WAAA,GAAAnG,IAAI,CAACM,IAAI,cAAA6F,WAAA,uBAATA,WAAA,CAAW5F,IAAI,MAAK,QAAQ,EAAE;MACvCR,QAAQ,CAAC,kBAAkB,CAAC;IAC9B,CAAC,MAAM,IAAI,EAAAqG,WAAA,GAAApG,IAAI,CAACM,IAAI,cAAA8F,WAAA,uBAATA,WAAA,CAAW7F,IAAI,MAAK,QAAQ,EAAE;MACvCR,QAAQ,CAAC,kBAAkB,CAAC;IAC9B,CAAC,MAAM;MACLA,QAAQ,CAAC,GAAG,CAAC;IACf;IAEAP,KAAK,CAAC6G,OAAO,CAAC,uBAAuB,CAAC;EACxC,CAAC;EAED,MAAMxE,OAAO,GAAGA,CAAA,KAAM;IACpB1B,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;;IAElD;IACA,IAAIkB,SAAS,CAACe,OAAO,EAAE;MACrBlC,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAC3DkB,SAAS,CAACe,OAAO,CAACW,IAAI,CAAC,OAAO,EAAE,eAAelD,aAAa,EAAE,CAAC;MAC/DwB,SAAS,CAACe,OAAO,CAACiE,GAAG,CAAC,CAAC,CAAC,CAAC;MACzBhF,SAAS,CAACe,OAAO,CAACkE,UAAU,CAAC,CAAC;MAC9BjF,SAAS,CAACe,OAAO,GAAG,IAAI;IAC1B;;IAEA;IACA,IAAId,iBAAiB,CAACc,OAAO,EAAE;MAC7BlC,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;MACzDmB,iBAAiB,CAACc,OAAO,CAACiC,cAAc,GAAG,IAAI;MAC/C/C,iBAAiB,CAACc,OAAO,CAACmC,OAAO,GAAG,IAAI;MACxCjD,iBAAiB,CAACc,OAAO,CAACmE,KAAK,CAAC,CAAC;MACjCjF,iBAAiB,CAACc,OAAO,GAAG,IAAI;IAClC;;IAEA;IACA,IAAIb,cAAc,CAACa,OAAO,EAAE;MAC1BlC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACvDoB,cAAc,CAACa,OAAO,CAACwC,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAI;QAClDA,KAAK,CAAC0B,IAAI,CAAC,CAAC;QACZtG,OAAO,CAACC,GAAG,CAAC,iCAAiC2E,KAAK,CAAC2B,IAAI,EAAE,CAAC;MAC5D,CAAC,CAAC;MACFlF,cAAc,CAACa,OAAO,GAAG,IAAI;IAC/B;;IAEA;IACA,IAAIjB,aAAa,CAACiB,OAAO,EAAE;MACzBjB,aAAa,CAACiB,OAAO,CAACyB,SAAS,GAAG,IAAI;IACxC;IACA,IAAIzC,cAAc,CAACgB,OAAO,EAAE;MAC1BhB,cAAc,CAACgB,OAAO,CAACyB,SAAS,GAAG,IAAI;IACzC;;IAEA;IACArD,cAAc,CAAC,KAAK,CAAC;IACrBU,mBAAmB,CAAC,CAAC,CAAC;IACtBF,mBAAmB,CAAC,kBAAkB,CAAC;IAEvCd,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;EAC9C,CAAC;EAED,oBACET,OAAA;IAAKgH,SAAS,EAAC,qCAAqC;IAAAC,QAAA,gBAElDjH,OAAA;MAAKgH,SAAS,EAAC,gDAAgD;MAAAC,QAAA,eAC7DjH,OAAA;QAAKgH,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDjH,OAAA;UAAAiH,QAAA,gBACEjH,OAAA;YAAIgH,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxErH,OAAA;YAAGgH,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,WAAS,EAAC9G,aAAa;UAAA;YAAA+G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACNrH,OAAA;UAAKgH,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBjH,OAAA;YAAGgH,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAE5F;UAAgB;YAAA6F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3DrH,OAAA;YAAGgH,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAE1F,gBAAgB,EAAC,2BAAU;UAAA;YAAA2F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrH,OAAA;MAAKgH,SAAS,EAAC,YAAY;MAAAC,QAAA,eACzBjH,OAAA;QAAKgH,SAAS,EAAC,6DAA6D;QAAAC,QAAA,gBAE1EjH,OAAA;UAAKgH,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAC9DjH,OAAA;YACEsH,GAAG,EAAE7F,aAAc;YACnB8F,QAAQ;YACRC,WAAW;YACXnB,KAAK;YACLW,SAAS,EAAC;UAA4B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACFrH,OAAA;YAAKgH,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChFjH,OAAA;cAAMgH,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,EACLpG,YAAY,iBACXjB,OAAA;YAAKgH,SAAS,EAAC,+DAA+D;YAAAC,QAAA,eAC5EjH,OAAA,CAACN,oBAAoB;cAACsH,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNrH,OAAA;UAAKgH,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAC9DjH,OAAA;YACEsH,GAAG,EAAE5F,cAAe;YACpB6F,QAAQ;YACRC,WAAW;YACXR,SAAS,EAAC;UAA4B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACFrH,OAAA;YAAKgH,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChFjH,OAAA;cAAMgH,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,EACL,CAACxG,WAAW,iBACXb,OAAA;YAAKgH,SAAS,EAAC,+DAA+D;YAAAC,QAAA,eAC5EjH,OAAA;cAAKgH,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BjH,OAAA,CAACN,oBAAoB;gBAACsH,SAAS,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzErH,OAAA;gBAAGgH,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrH,OAAA;MAAKgH,SAAS,EAAC,oDAAoD;MAAAC,QAAA,eACjEjH,OAAA;QAAKgH,SAAS,EAAC,6DAA6D;QAAAC,QAAA,gBAE1EjH,OAAA;UACEyH,OAAO,EAAE5B,WAAY;UACrBmB,SAAS,EAAE,sCACTjG,YAAY,GACR,6BAA6B,GAC7B,+BAA+B,EAClC;UAAAkG,QAAA,EAEFlG,YAAY,gBACXf,OAAA,CAACP,mBAAmB;YAACuH,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAE3CrH,OAAA,CAACX,cAAc;YAAC2H,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACtC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAGTrH,OAAA;UACEyH,OAAO,EAAExB,WAAY;UACrBe,SAAS,EAAE,sCACT/F,YAAY,GACR,6BAA6B,GAC7B,+BAA+B,EAClC;UAAAgG,QAAA,EAEFhG,YAAY,gBACXjB,OAAA,CAACN,oBAAoB;YAACsH,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAE5CrH,OAAA,CAACV,eAAe;YAAC0H,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACvC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAGTrH,OAAA;UACEyH,OAAO,EAAErB,aAAc;UACvBY,SAAS,EAAE,sCACT,CAAC7F,WAAW,GACR,6BAA6B,GAC7B,+BAA+B,EAClC;UAAA8F,QAAA,EAEF9F,WAAW,gBACVnB,OAAA,CAACR,eAAe;YAACwH,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEvCrH,OAAA,CAACL,oBAAoB;YAACqH,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAC5C;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAGTrH,OAAA;UACEyH,OAAO,EAAEnB,OAAQ;UACjBU,SAAS,EAAC,gEAAgE;UAAAC,QAAA,eAE1EjH,OAAA,CAACT,cAAc;YAACyH,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnH,EAAA,CAheID,WAAW;EAAA,QACWd,SAAS,EAClBC,WAAW,EACIU,OAAO;AAAA;AAAA4H,EAAA,GAHnCzH,WAAW;AAkejB,eAAeA,WAAW;AAAC,IAAAyH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}