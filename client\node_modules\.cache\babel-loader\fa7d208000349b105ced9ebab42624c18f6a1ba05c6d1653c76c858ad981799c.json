{"ast": null, "code": "var _jsxFileName = \"C:\\\\Projeler\\\\kidgarden\\\\burky_root_web\\\\client\\\\src\\\\pages\\\\meeting\\\\MeetingPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { MicrophoneIcon, VideoCameraIcon, PhoneXMarkIcon, SpeakerWaveIcon } from '@heroicons/react/24/outline';\nimport { MicrophoneIcon as MicrophoneIconSolid, VideoCameraIcon as VideoCameraIconSolid, SpeakerWaveIcon as SpeakerWaveIconSolid } from '@heroicons/react/24/solid';\nimport io from 'socket.io-client';\nimport toast from 'react-hot-toast';\nimport { useAuth } from '../../hooks/useAuth';\n\n/**\n * P2P WebRTC Meeting Sayfası\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MeetingPage = () => {\n  _s();\n  const {\n    appointmentId\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    user,\n    hasPermission\n  } = useAuth();\n\n  // Permission check is now handled by PagePermission component\n  useEffect(() => {\n    var _user$role;\n    console.log('🔍 MeetingPage initialized for appointment:', appointmentId);\n    console.log('👤 User:', user ? {\n      id: user.id,\n      role: (_user$role = user.role) === null || _user$role === void 0 ? void 0 : _user$role.name\n    } : 'Not logged in');\n  }, [user, appointmentId]);\n\n  // State\n  const [isConnected, setIsConnected] = useState(false);\n  const [isAudioMuted, setIsAudioMuted] = useState(false);\n  const [isVideoMuted, setIsVideoMuted] = useState(false);\n  const [isSpeakerOn, setIsSpeakerOn] = useState(true);\n  const [connectionStatus, setConnectionStatus] = useState('Bağlanıyor...');\n  const [participantCount, setParticipantCount] = useState(0);\n  const [isInitiator, setIsInitiator] = useState(false);\n  const [retryCount, setRetryCount] = useState(0);\n\n  // Refs\n  const localVideoRef = useRef(null);\n  const remoteVideoRef = useRef(null);\n  const socketRef = useRef(null);\n  const peerConnectionRef = useRef(null);\n  const localStreamRef = useRef(null);\n  const hasJoinedRoom = useRef(false);\n  const retryTimeoutRef = useRef(null);\n\n  // WebRTC Configuration\n  const rtcConfig = {\n    iceServers: [{\n      urls: 'stun:stun.l.google.com:19302'\n    }, {\n      urls: 'stun:stun1.l.google.com:19302'\n    }]\n  };\n  useEffect(() => {\n    var _socketRef$current;\n    let isMounted = true;\n\n    // Sadece user ve appointmentId varsa ve henüz bağlantı kurulmamışsa bağlantı kur\n    if (user !== null && user !== void 0 && user.id && appointmentId && !((_socketRef$current = socketRef.current) !== null && _socketRef$current !== void 0 && _socketRef$current.connected)) {\n      console.log('🚀 MEETING: Bağlantı başlatılıyor...', {\n        userId: user.id,\n        appointmentId\n      });\n\n      // Async function'ı çağır ama mount kontrolü yap\n      const initConnection = async () => {\n        if (isMounted) {\n          await initializeConnection();\n        }\n      };\n      initConnection();\n    }\n    return () => {\n      isMounted = false;\n      console.log('🧹 MEETING: Component unmount, cleanup çağrılıyor...');\n\n      // Sadece gerçek unmount'ta cleanup yap (StrictMode'da değil)\n      setTimeout(() => {\n        if (!isMounted) {\n          cleanup();\n        }\n      }, 500);\n    };\n  }, [user === null || user === void 0 ? void 0 : user.id, appointmentId]);\n  const initializeConnection = async () => {\n    try {\n      var _socketRef$current2, _socketRef$current3;\n      // Eğer zaten socket bağlantısı varsa, yenisini oluşturma\n      if ((_socketRef$current2 = socketRef.current) !== null && _socketRef$current2 !== void 0 && _socketRef$current2.connected) {\n        console.log('⚠️ MEETING: Socket zaten bağlı, yeni bağlantı oluşturulmuyor');\n        return;\n      }\n\n      // Socket.IO bağlantısı - diğer sayfalarla tutarlı URL kullan\n      const socketUrl = process.env.REACT_APP_API_URL || 'https://192.168.1.111:5000';\n      console.log('🔌 MEETING: Socket.IO bağlantısı deneniyor:', socketUrl);\n\n      // Token'ı al (authentication için)\n      const token = localStorage.getItem('accessToken');\n      socketRef.current = io(socketUrl, {\n        auth: {\n          token\n        },\n        transports: ['websocket', 'polling'],\n        timeout: 10000,\n        reconnection: true,\n        reconnectionDelay: 1000,\n        reconnectionAttempts: 5,\n        forceNew: false,\n        // Mevcut bağlantıyı yeniden kullan\n        rejectUnauthorized: false,\n        // Self-signed sertifika için\n        secure: true,\n        upgrade: true,\n        rememberUpgrade: false\n      });\n\n      // Socket event listeners\n      setupSocketListeners();\n\n      // Kullanıcı medyasını al\n      await getUserMedia();\n\n      // Odaya katıl (sadece bir kez ve socket hala bağlıysa)\n      if (!hasJoinedRoom.current && (_socketRef$current3 = socketRef.current) !== null && _socketRef$current3 !== void 0 && _socketRef$current3.connected) {\n        const roomId = `appointment-${appointmentId}`;\n        console.log('🚪 MEETING: Odaya katılım isteği gönderiliyor:', roomId);\n        socketRef.current.emit('join', roomId);\n        hasJoinedRoom.current = true;\n      } else if (hasJoinedRoom.current) {\n        console.log('⚠️ MEETING: Zaten odaya katılım yapılmış, tekrar katılım atlanıyor');\n      } else {\n        console.log('❌ MEETING: Socket bağlantısı yok, odaya katılım yapılamıyor');\n      }\n    } catch (error) {\n      console.error('Bağlantı hatası:', error);\n      toast.error('Bağlantı kurulamadı');\n      setConnectionStatus('Bağlantı hatası');\n    }\n  };\n  const setupSocketListeners = () => {\n    const socket = socketRef.current;\n\n    // Connection events\n    socket.on('connect', () => {\n      console.log('✅ MEETING: Socket.IO bağlantısı kuruldu:', socket.id);\n      setConnectionStatus('Bağlandı, odaya katılıyor...');\n    });\n    socket.on('disconnect', reason => {\n      console.log('❌ MEETING: Socket.IO bağlantısı kesildi:', reason);\n      setConnectionStatus('Bağlantı kesildi');\n      setIsConnected(false);\n    });\n    socket.on('connect_error', error => {\n      console.error('🔥 MEETING: Socket.IO bağlantı hatası:', error);\n      setConnectionStatus('Bağlantı hatası');\n    });\n    socket.on('joined', ({\n      roomId,\n      roomSize\n    }) => {\n      console.log(`✅ MEETING: Odaya katıldı: ${roomId}, Katılımcı sayısı: ${roomSize}`);\n      setParticipantCount(roomSize);\n      setConnectionStatus(`Odada ${roomSize} kişi`);\n      if (roomSize === 1) {\n        setConnectionStatus('Karşı tarafı bekleniyor...');\n        setIsInitiator(false); // İlk katılan initiator değil\n        console.log('👤 MEETING: İlk katılan - Initiator: false');\n      } else if (roomSize === 2) {\n        setIsInitiator(true); // İkinci katılan initiator olur\n        console.log('👤 MEETING: İkinci katılan - Initiator: true');\n      }\n    });\n    socket.on('ready', ({\n      isInitiator\n    }) => {\n      console.log(`🚀 MEETING: Oda hazır, WebRTC bağlantısı başlatılıyor - Initiator: ${isInitiator}`);\n      setConnectionStatus('Bağlantı kuruluyor...');\n      setIsInitiator(isInitiator);\n\n      // Her iki taraf da peer connection oluşturur\n      createPeerConnection();\n\n      // Kısa bir gecikme sonra initiator offer gönderir\n      setTimeout(() => {\n        if (isInitiator) {\n          console.log('🚀 MEETING: Initiator olarak offer gönderiliyor...');\n          createOffer();\n        } else {\n          console.log('⏳ MEETING: Offer bekleniyor...');\n        }\n      }, 500);\n    });\n    socket.on('full', roomId => {\n      console.log('Oda dolu:', roomId);\n      toast.error('Görüşme odası dolu');\n      setConnectionStatus('Oda dolu');\n    });\n    socket.on('offer', async sdp => {\n      console.log('Offer alındı');\n      await handleOffer(sdp);\n    });\n    socket.on('answer', async sdp => {\n      console.log('Answer alındı');\n      await handleAnswer(sdp);\n    });\n    socket.on('candidate', async candidate => {\n      console.log('ICE candidate alındı');\n      await handleCandidate(candidate);\n    });\n    socket.on('leave', () => {\n      console.log('Karşı taraf ayrıldı');\n      toast('Karşı taraf görüşmeden ayrıldı', {\n        icon: 'ℹ️'\n      });\n      setConnectionStatus('Karşı taraf ayrıldı');\n      setParticipantCount(1);\n\n      // Remote video'yu temizle\n      if (remoteVideoRef.current) {\n        remoteVideoRef.current.srcObject = null;\n      }\n    });\n  };\n  const getUserMedia = async () => {\n    try {\n      // Eğer zaten stream varsa, yenisini alma\n      if (localStreamRef.current) {\n        console.log('📹 MEETING: Local stream zaten mevcut');\n        return localStreamRef.current;\n      }\n      console.log('📹 MEETING: Kullanıcı medyası isteniyor...');\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: true,\n        audio: true\n      });\n      localStreamRef.current = stream;\n      if (localVideoRef.current) {\n        localVideoRef.current.srcObject = stream;\n      }\n      console.log('✅ MEETING: Kullanıcı medyası alındı');\n      return stream;\n    } catch (error) {\n      console.error('❌ MEETING: Medya erişim hatası:', error);\n      toast.error('Kamera ve mikrofon erişimi gerekli');\n      throw error;\n    }\n  };\n  const createPeerConnection = () => {\n    // Eğer zaten peer connection varsa, yenisini oluşturma\n    if (peerConnectionRef.current) {\n      console.log('⚠️ MEETING: Peer connection zaten mevcut, yenisi oluşturulmuyor');\n      return peerConnectionRef.current;\n    }\n    console.log('🔗 MEETING: Yeni peer connection oluşturuluyor...');\n    const pc = new RTCPeerConnection(rtcConfig);\n    peerConnectionRef.current = pc;\n\n    // ICE candidate event\n    pc.onicecandidate = event => {\n      if (event.candidate) {\n        socketRef.current.emit('candidate', {\n          roomId: `appointment-${appointmentId}`,\n          candidate: event.candidate\n        });\n      }\n    };\n\n    // Remote stream event\n    pc.ontrack = event => {\n      console.log('Remote stream alındı');\n      const [remoteStream] = event.streams;\n      if (remoteVideoRef.current) {\n        remoteVideoRef.current.srcObject = remoteStream;\n      }\n      setIsConnected(true);\n      setConnectionStatus('Bağlantı kuruldu');\n    };\n\n    // Connection state change\n    pc.onconnectionstatechange = () => {\n      console.log('Connection state:', pc.connectionState);\n      if (pc.connectionState === 'connected') {\n        setIsConnected(true);\n        setConnectionStatus('Bağlantı kuruldu');\n        setRetryCount(0); // Başarılı bağlantıda retry sayacını sıfırla\n\n        // Retry timeout'unu temizle\n        if (retryTimeoutRef.current) {\n          clearTimeout(retryTimeoutRef.current);\n          retryTimeoutRef.current = null;\n        }\n      } else if (pc.connectionState === 'disconnected') {\n        setIsConnected(false);\n        setConnectionStatus('Bağlantı kesildi');\n      } else if (pc.connectionState === 'failed') {\n        console.log('❌ MEETING: WebRTC bağlantısı başarısız oldu');\n        setIsConnected(false);\n        setConnectionStatus('Bağlantı başarısız, yeniden deneniyor...');\n\n        // Otomatik yeniden deneme\n        handleConnectionFailure();\n      }\n    };\n\n    // Local stream'i peer connection'a ekle\n    if (localStreamRef.current) {\n      localStreamRef.current.getTracks().forEach(track => {\n        pc.addTrack(track, localStreamRef.current);\n      });\n    }\n  };\n  const createOffer = async () => {\n    try {\n      const pc = peerConnectionRef.current;\n      if (!pc) {\n        console.error('❌ MEETING: Peer connection bulunamadı');\n        return;\n      }\n      if (pc.signalingState !== 'stable') {\n        console.log(`⚠️ MEETING: Yanlış signaling state: ${pc.signalingState}, offer atlanıyor`);\n        return;\n      }\n      console.log('📤 MEETING: Offer oluşturuluyor...');\n      const offer = await pc.createOffer({\n        offerToReceiveAudio: true,\n        offerToReceiveVideo: true\n      });\n      console.log('🔄 MEETING: Local description ayarlanıyor...');\n      await pc.setLocalDescription(offer);\n      console.log('📤 MEETING: Offer gönderiliyor...');\n      socketRef.current.emit('offer', {\n        roomId: `appointment-${appointmentId}`,\n        sdp: offer\n      });\n    } catch (error) {\n      console.error('❌ MEETING: Offer oluşturma hatası:', error);\n    }\n  };\n  const handleOffer = async sdp => {\n    try {\n      console.log('📥 MEETING: Offer alındı, işleniyor...');\n\n      // Peer connection yoksa oluştur\n      if (!peerConnectionRef.current) {\n        createPeerConnection();\n      }\n      const pc = peerConnectionRef.current;\n\n      // State kontrolü\n      if (pc.signalingState !== 'stable' && pc.signalingState !== 'have-local-offer') {\n        console.log(`⚠️ MEETING: Yanlış signaling state: ${pc.signalingState}, offer atlanıyor`);\n        return;\n      }\n      console.log('🔄 MEETING: Remote description ayarlanıyor...');\n      await pc.setRemoteDescription(new RTCSessionDescription(sdp));\n      console.log('📤 MEETING: Answer oluşturuluyor...');\n      const answer = await pc.createAnswer();\n      await pc.setLocalDescription(answer);\n      console.log('📤 MEETING: Answer gönderiliyor...');\n      socketRef.current.emit('answer', {\n        roomId: `appointment-${appointmentId}`,\n        sdp: answer\n      });\n    } catch (error) {\n      console.error('❌ MEETING: Offer işleme hatası:', error);\n    }\n  };\n  const handleAnswer = async sdp => {\n    try {\n      console.log('📥 MEETING: Answer alındı, işleniyor...');\n      const pc = peerConnectionRef.current;\n      if (!pc) {\n        console.error('❌ MEETING: Peer connection bulunamadı');\n        return;\n      }\n\n      // State kontrolü\n      if (pc.signalingState !== 'have-local-offer') {\n        console.log(`⚠️ MEETING: Yanlış signaling state: ${pc.signalingState}, answer atlanıyor`);\n        return;\n      }\n      console.log('🔄 MEETING: Remote description (answer) ayarlanıyor...');\n      await pc.setRemoteDescription(new RTCSessionDescription(sdp));\n      console.log('✅ MEETING: Answer başarıyla işlendi');\n    } catch (error) {\n      console.error('❌ MEETING: Answer işleme hatası:', error);\n    }\n  };\n  const handleCandidate = async candidate => {\n    try {\n      const pc = peerConnectionRef.current;\n      if (!pc) {\n        console.log('⚠️ MEETING: Peer connection yok, ICE candidate atlanıyor');\n        return;\n      }\n      if (pc.remoteDescription) {\n        console.log('🧊 MEETING: ICE candidate ekleniyor...');\n        await pc.addIceCandidate(new RTCIceCandidate(candidate));\n        console.log('✅ MEETING: ICE candidate başarıyla eklendi');\n      } else {\n        console.log('⚠️ MEETING: Remote description yok, ICE candidate atlanıyor');\n      }\n    } catch (error) {\n      console.error('❌ MEETING: ICE candidate işleme hatası:', error);\n    }\n  };\n  const toggleAudio = () => {\n    if (localStreamRef.current) {\n      const audioTrack = localStreamRef.current.getAudioTracks()[0];\n      if (audioTrack) {\n        audioTrack.enabled = !audioTrack.enabled;\n        setIsAudioMuted(!audioTrack.enabled);\n      }\n    }\n  };\n  const toggleVideo = () => {\n    if (localStreamRef.current) {\n      const videoTrack = localStreamRef.current.getVideoTracks()[0];\n      if (videoTrack) {\n        videoTrack.enabled = !videoTrack.enabled;\n        setIsVideoMuted(!videoTrack.enabled);\n      }\n    }\n  };\n  const toggleSpeaker = () => {\n    if (remoteVideoRef.current) {\n      remoteVideoRef.current.muted = isSpeakerOn;\n      setIsSpeakerOn(!isSpeakerOn);\n    }\n  };\n  const endCall = () => {\n    var _user$role2, _user$role3, _user$role4;\n    cleanup();\n\n    // Kullanıcının rolüne göre doğru sayfaya yönlendir\n    if (((_user$role2 = user.role) === null || _user$role2 === void 0 ? void 0 : _user$role2.name) === 'Admin') {\n      navigate('/admin/dashboard');\n    } else if (((_user$role3 = user.role) === null || _user$role3 === void 0 ? void 0 : _user$role3.name) === 'Expert') {\n      navigate('/expert/sessions');\n    } else if (((_user$role4 = user.role) === null || _user$role4 === void 0 ? void 0 : _user$role4.name) === 'Client') {\n      navigate('/client/sessions');\n    } else {\n      navigate('/');\n    }\n    toast.success('Görüşme sonlandırıldı');\n  };\n  const cleanup = () => {\n    console.log('🧹 MEETING: Cleanup başlatılıyor...');\n\n    // Socket bağlantısını kapat\n    if (socketRef.current) {\n      console.log('🔌 MEETING: Socket bağlantısı kapatılıyor...');\n      try {\n        // Sadece bağlıysa leave event'i gönder\n        if (socketRef.current.connected) {\n          socketRef.current.emit('leave', `appointment-${appointmentId}`);\n        }\n\n        // Event listener'ları kaldır\n        socketRef.current.off();\n\n        // Bağlantıyı kapat\n        socketRef.current.disconnect();\n      } catch (error) {\n        console.error('❌ MEETING: Socket cleanup hatası:', error);\n      } finally {\n        socketRef.current = null;\n      }\n    }\n\n    // Peer connection'ı kapat\n    if (peerConnectionRef.current) {\n      console.log('🔗 MEETING: Peer connection kapatılıyor...');\n      peerConnectionRef.current.onicecandidate = null;\n      peerConnectionRef.current.ontrack = null;\n      peerConnectionRef.current.close();\n      peerConnectionRef.current = null;\n    }\n\n    // Local stream'i durdur\n    if (localStreamRef.current) {\n      console.log('📹 MEETING: Local stream durduruluyor...');\n      localStreamRef.current.getTracks().forEach(track => {\n        track.stop();\n        console.log(`📹 MEETING: Track durduruldu: ${track.kind}`);\n      });\n      localStreamRef.current = null;\n    }\n\n    // Video elementlerini temizle\n    if (localVideoRef.current) {\n      localVideoRef.current.srcObject = null;\n    }\n    if (remoteVideoRef.current) {\n      remoteVideoRef.current.srcObject = null;\n    }\n\n    // State'leri sıfırla\n    setIsConnected(false);\n    setParticipantCount(0);\n    setConnectionStatus('Bağlantı kesildi');\n    setIsInitiator(false);\n    hasJoinedRoom.current = false;\n    console.log('✅ MEETING: Cleanup tamamlandı');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-900 text-white\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 px-6 py-4 border-b border-gray-700\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-xl font-semibold\",\n            children: \"Psikolojik Dan\\u0131\\u015Fmanl\\u0131k Seans\\u0131\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-400\",\n            children: [\"Randevu #\", appointmentId]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 550,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-right\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-400\",\n            children: connectionStatus\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-500\",\n            children: [participantCount, \" kat\\u0131l\\u0131mc\\u0131\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 556,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 549,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 548,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 h-[calc(100vh-200px)]\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative bg-gray-800 rounded-lg overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"video\", {\n            ref: localVideoRef,\n            autoPlay: true,\n            playsInline: true,\n            muted: true,\n            className: \"w-full h-full object-cover\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-4 left-4 bg-black bg-opacity-50 px-3 py-1 rounded\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm\",\n              children: \"Siz\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 574,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 573,\n            columnNumber: 13\n          }, this), isVideoMuted && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gray-700 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(VideoCameraIconSolid, {\n              className: \"h-16 w-16 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 577,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 565,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative bg-gray-800 rounded-lg overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"video\", {\n            ref: remoteVideoRef,\n            autoPlay: true,\n            playsInline: true,\n            className: \"w-full h-full object-cover\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-4 left-4 bg-black bg-opacity-50 px-3 py-1 rounded\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm\",\n              children: \"Kar\\u015F\\u0131 Taraf\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 591,\n            columnNumber: 13\n          }, this), !isConnected && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gray-700 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(VideoCameraIconSolid, {\n                className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 597,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-400\",\n                children: \"Kar\\u015F\\u0131 taraf bekleniyor...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 596,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 595,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 563,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 562,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-6 left-1/2 transform -translate-x-1/2\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-4 bg-gray-800 rounded-full px-6 py-3 shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: toggleAudio,\n          className: `p-3 rounded-full transition-colors ${isAudioMuted ? 'bg-red-600 hover:bg-red-700' : 'bg-gray-600 hover:bg-gray-700'}`,\n          children: isAudioMuted ? /*#__PURE__*/_jsxDEV(MicrophoneIconSolid, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 619,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(MicrophoneIcon, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 621,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 610,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: toggleVideo,\n          className: `p-3 rounded-full transition-colors ${isVideoMuted ? 'bg-red-600 hover:bg-red-700' : 'bg-gray-600 hover:bg-gray-700'}`,\n          children: isVideoMuted ? /*#__PURE__*/_jsxDEV(VideoCameraIconSolid, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(VideoCameraIcon, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 637,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 626,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: toggleSpeaker,\n          className: `p-3 rounded-full transition-colors ${!isSpeakerOn ? 'bg-red-600 hover:bg-red-700' : 'bg-gray-600 hover:bg-gray-700'}`,\n          children: isSpeakerOn ? /*#__PURE__*/_jsxDEV(SpeakerWaveIcon, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 651,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(SpeakerWaveIconSolid, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 653,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 642,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: endCall,\n          className: \"p-3 rounded-full bg-red-600 hover:bg-red-700 transition-colors\",\n          children: /*#__PURE__*/_jsxDEV(PhoneXMarkIcon, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 662,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 658,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 608,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 607,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 546,\n    columnNumber: 5\n  }, this);\n};\n_s(MeetingPage, \"VCftRvGIPztOb3M4jPgc1OyemHI=\", false, function () {\n  return [useParams, useNavigate, useAuth];\n});\n_c = MeetingPage;\nexport default MeetingPage;\nvar _c;\n$RefreshReg$(_c, \"MeetingPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useParams", "useNavigate", "MicrophoneIcon", "VideoCameraIcon", "PhoneXMarkIcon", "SpeakerWaveIcon", "MicrophoneIconSolid", "VideoCameraIconSolid", "SpeakerWaveIconSolid", "io", "toast", "useAuth", "jsxDEV", "_jsxDEV", "MeetingPage", "_s", "appointmentId", "navigate", "user", "hasPermission", "_user$role", "console", "log", "id", "role", "name", "isConnected", "setIsConnected", "isAudioMuted", "setIsAudioMuted", "isVideoMuted", "setIsVideoMuted", "isSpeakerOn", "setIsSpeakerOn", "connectionStatus", "setConnectionStatus", "participantCount", "setParticipantCount", "isInitiator", "setIsInitiator", "retryCount", "setRetryCount", "localVideoRef", "remoteVideoRef", "socketRef", "peerConnectionRef", "localStreamRef", "hasJoinedRoom", "retryTimeoutRef", "rtcConfig", "iceServers", "urls", "_socketRef$current", "isMounted", "current", "connected", "userId", "initConnection", "initializeConnection", "setTimeout", "cleanup", "_socketRef$current2", "_socketRef$current3", "socketUrl", "process", "env", "REACT_APP_API_URL", "token", "localStorage", "getItem", "auth", "transports", "timeout", "reconnection", "reconnectionDelay", "reconnectionAttempts", "forceNew", "rejectUnauthorized", "secure", "upgrade", "rememberUpgrade", "setupSocketListeners", "getUserMedia", "roomId", "emit", "error", "socket", "on", "reason", "roomSize", "createPeerConnection", "createOffer", "sdp", "handleOffer", "handleAnswer", "candidate", "handleCandidate", "icon", "srcObject", "stream", "navigator", "mediaDevices", "video", "audio", "pc", "RTCPeerConnection", "onicecandidate", "event", "ontrack", "remoteStream", "streams", "onconnectionstatechange", "connectionState", "clearTimeout", "handleConnectionFailure", "getTracks", "for<PERSON>ach", "track", "addTrack", "signalingState", "offer", "offerToReceiveAudio", "offerToReceiveVideo", "setLocalDescription", "setRemoteDescription", "RTCSessionDescription", "answer", "createAnswer", "remoteDescription", "addIceCandidate", "RTCIceCandidate", "toggleAudio", "audioTrack", "getAudioTracks", "enabled", "toggleVideo", "videoTrack", "getVideoTracks", "toggleSpeaker", "muted", "endCall", "_user$role2", "_user$role3", "_user$role4", "success", "off", "disconnect", "close", "stop", "kind", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "autoPlay", "playsInline", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Projeler/kidgarden/burky_root_web/client/src/pages/meeting/MeetingPage.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport {\n  MicrophoneIcon,\n  VideoCameraIcon,\n  PhoneXMarkIcon,\n  SpeakerWaveIcon\n} from '@heroicons/react/24/outline';\nimport {\n  MicrophoneIcon as MicrophoneIconSolid,\n  VideoCameraIcon as VideoCameraIconSolid,\n  SpeakerWaveIcon as SpeakerWaveIconSolid\n} from '@heroicons/react/24/solid';\nimport io from 'socket.io-client';\nimport toast from 'react-hot-toast';\nimport { useAuth } from '../../hooks/useAuth';\n\n/**\n * P2P WebRTC Meeting Sayfası\n */\nconst MeetingPage = () => {\n  const { appointmentId } = useParams();\n  const navigate = useNavigate();\n  const { user, hasPermission } = useAuth();\n\n  // Permission check is now handled by PagePermission component\n  useEffect(() => {\n    console.log('🔍 MeetingPage initialized for appointment:', appointmentId);\n    console.log('👤 User:', user ? { id: user.id, role: user.role?.name } : 'Not logged in');\n  }, [user, appointmentId]);\n  \n  // State\n  const [isConnected, setIsConnected] = useState(false);\n  const [isAudioMuted, setIsAudioMuted] = useState(false);\n  const [isVideoMuted, setIsVideoMuted] = useState(false);\n  const [isSpeakerOn, setIsSpeakerOn] = useState(true);\n  const [connectionStatus, setConnectionStatus] = useState('Bağlanıyor...');\n  const [participantCount, setParticipantCount] = useState(0);\n  const [isInitiator, setIsInitiator] = useState(false);\n  const [retryCount, setRetryCount] = useState(0);\n  \n  // Refs\n  const localVideoRef = useRef(null);\n  const remoteVideoRef = useRef(null);\n  const socketRef = useRef(null);\n  const peerConnectionRef = useRef(null);\n  const localStreamRef = useRef(null);\n  const hasJoinedRoom = useRef(false);\n  const retryTimeoutRef = useRef(null);\n  \n  // WebRTC Configuration\n  const rtcConfig = {\n    iceServers: [\n      { urls: 'stun:stun.l.google.com:19302' },\n      { urls: 'stun:stun1.l.google.com:19302' },\n    ],\n  };\n\n  useEffect(() => {\n    let isMounted = true;\n\n    // Sadece user ve appointmentId varsa ve henüz bağlantı kurulmamışsa bağlantı kur\n    if (user?.id && appointmentId && !socketRef.current?.connected) {\n      console.log('🚀 MEETING: Bağlantı başlatılıyor...', { userId: user.id, appointmentId });\n\n      // Async function'ı çağır ama mount kontrolü yap\n      const initConnection = async () => {\n        if (isMounted) {\n          await initializeConnection();\n        }\n      };\n\n      initConnection();\n    }\n\n    return () => {\n      isMounted = false;\n      console.log('🧹 MEETING: Component unmount, cleanup çağrılıyor...');\n\n      // Sadece gerçek unmount'ta cleanup yap (StrictMode'da değil)\n      setTimeout(() => {\n        if (!isMounted) {\n          cleanup();\n        }\n      }, 500);\n    };\n  }, [user?.id, appointmentId]);\n\n  const initializeConnection = async () => {\n    try {\n      // Eğer zaten socket bağlantısı varsa, yenisini oluşturma\n      if (socketRef.current?.connected) {\n        console.log('⚠️ MEETING: Socket zaten bağlı, yeni bağlantı oluşturulmuyor');\n        return;\n      }\n\n      // Socket.IO bağlantısı - diğer sayfalarla tutarlı URL kullan\n      const socketUrl = process.env.REACT_APP_API_URL || 'https://192.168.1.111:5000';\n      console.log('🔌 MEETING: Socket.IO bağlantısı deneniyor:', socketUrl);\n\n      // Token'ı al (authentication için)\n      const token = localStorage.getItem('accessToken');\n\n      socketRef.current = io(socketUrl, {\n        auth: { token },\n        transports: ['websocket', 'polling'],\n        timeout: 10000,\n        reconnection: true,\n        reconnectionDelay: 1000,\n        reconnectionAttempts: 5,\n        forceNew: false, // Mevcut bağlantıyı yeniden kullan\n        rejectUnauthorized: false, // Self-signed sertifika için\n        secure: true,\n        upgrade: true,\n        rememberUpgrade: false\n      });\n      \n      // Socket event listeners\n      setupSocketListeners();\n      \n      // Kullanıcı medyasını al\n      await getUserMedia();\n\n      // Odaya katıl (sadece bir kez ve socket hala bağlıysa)\n      if (!hasJoinedRoom.current && socketRef.current?.connected) {\n        const roomId = `appointment-${appointmentId}`;\n        console.log('🚪 MEETING: Odaya katılım isteği gönderiliyor:', roomId);\n        socketRef.current.emit('join', roomId);\n        hasJoinedRoom.current = true;\n      } else if (hasJoinedRoom.current) {\n        console.log('⚠️ MEETING: Zaten odaya katılım yapılmış, tekrar katılım atlanıyor');\n      } else {\n        console.log('❌ MEETING: Socket bağlantısı yok, odaya katılım yapılamıyor');\n      }\n      \n    } catch (error) {\n      console.error('Bağlantı hatası:', error);\n      toast.error('Bağlantı kurulamadı');\n      setConnectionStatus('Bağlantı hatası');\n    }\n  };\n\n  const setupSocketListeners = () => {\n    const socket = socketRef.current;\n\n    // Connection events\n    socket.on('connect', () => {\n      console.log('✅ MEETING: Socket.IO bağlantısı kuruldu:', socket.id);\n      setConnectionStatus('Bağlandı, odaya katılıyor...');\n    });\n\n    socket.on('disconnect', (reason) => {\n      console.log('❌ MEETING: Socket.IO bağlantısı kesildi:', reason);\n      setConnectionStatus('Bağlantı kesildi');\n      setIsConnected(false);\n    });\n\n    socket.on('connect_error', (error) => {\n      console.error('🔥 MEETING: Socket.IO bağlantı hatası:', error);\n      setConnectionStatus('Bağlantı hatası');\n    });\n\n    socket.on('joined', ({ roomId, roomSize }) => {\n      console.log(`✅ MEETING: Odaya katıldı: ${roomId}, Katılımcı sayısı: ${roomSize}`);\n      setParticipantCount(roomSize);\n      setConnectionStatus(`Odada ${roomSize} kişi`);\n\n      if (roomSize === 1) {\n        setConnectionStatus('Karşı tarafı bekleniyor...');\n        setIsInitiator(false); // İlk katılan initiator değil\n        console.log('👤 MEETING: İlk katılan - Initiator: false');\n      } else if (roomSize === 2) {\n        setIsInitiator(true); // İkinci katılan initiator olur\n        console.log('👤 MEETING: İkinci katılan - Initiator: true');\n      }\n    });\n\n    socket.on('ready', ({ isInitiator }) => {\n      console.log(`🚀 MEETING: Oda hazır, WebRTC bağlantısı başlatılıyor - Initiator: ${isInitiator}`);\n      setConnectionStatus('Bağlantı kuruluyor...');\n      setIsInitiator(isInitiator);\n\n      // Her iki taraf da peer connection oluşturur\n      createPeerConnection();\n\n      // Kısa bir gecikme sonra initiator offer gönderir\n      setTimeout(() => {\n        if (isInitiator) {\n          console.log('🚀 MEETING: Initiator olarak offer gönderiliyor...');\n          createOffer();\n        } else {\n          console.log('⏳ MEETING: Offer bekleniyor...');\n        }\n      }, 500);\n    });\n\n    socket.on('full', (roomId) => {\n      console.log('Oda dolu:', roomId);\n      toast.error('Görüşme odası dolu');\n      setConnectionStatus('Oda dolu');\n    });\n\n    socket.on('offer', async (sdp) => {\n      console.log('Offer alındı');\n      await handleOffer(sdp);\n    });\n\n    socket.on('answer', async (sdp) => {\n      console.log('Answer alındı');\n      await handleAnswer(sdp);\n    });\n\n    socket.on('candidate', async (candidate) => {\n      console.log('ICE candidate alındı');\n      await handleCandidate(candidate);\n    });\n\n    socket.on('leave', () => {\n      console.log('Karşı taraf ayrıldı');\n      toast('Karşı taraf görüşmeden ayrıldı', { icon: 'ℹ️' });\n      setConnectionStatus('Karşı taraf ayrıldı');\n      setParticipantCount(1);\n\n      // Remote video'yu temizle\n      if (remoteVideoRef.current) {\n        remoteVideoRef.current.srcObject = null;\n      }\n    });\n  };\n\n  const getUserMedia = async () => {\n    try {\n      // Eğer zaten stream varsa, yenisini alma\n      if (localStreamRef.current) {\n        console.log('📹 MEETING: Local stream zaten mevcut');\n        return localStreamRef.current;\n      }\n\n      console.log('📹 MEETING: Kullanıcı medyası isteniyor...');\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: true,\n        audio: true\n      });\n\n      localStreamRef.current = stream;\n      if (localVideoRef.current) {\n        localVideoRef.current.srcObject = stream;\n      }\n\n      console.log('✅ MEETING: Kullanıcı medyası alındı');\n      return stream;\n    } catch (error) {\n      console.error('❌ MEETING: Medya erişim hatası:', error);\n      toast.error('Kamera ve mikrofon erişimi gerekli');\n      throw error;\n    }\n  };\n\n  const createPeerConnection = () => {\n    // Eğer zaten peer connection varsa, yenisini oluşturma\n    if (peerConnectionRef.current) {\n      console.log('⚠️ MEETING: Peer connection zaten mevcut, yenisi oluşturulmuyor');\n      return peerConnectionRef.current;\n    }\n\n    console.log('🔗 MEETING: Yeni peer connection oluşturuluyor...');\n    const pc = new RTCPeerConnection(rtcConfig);\n    peerConnectionRef.current = pc;\n\n    // ICE candidate event\n    pc.onicecandidate = (event) => {\n      if (event.candidate) {\n        socketRef.current.emit('candidate', {\n          roomId: `appointment-${appointmentId}`,\n          candidate: event.candidate\n        });\n      }\n    };\n\n    // Remote stream event\n    pc.ontrack = (event) => {\n      console.log('Remote stream alındı');\n      const [remoteStream] = event.streams;\n      if (remoteVideoRef.current) {\n        remoteVideoRef.current.srcObject = remoteStream;\n      }\n      setIsConnected(true);\n      setConnectionStatus('Bağlantı kuruldu');\n    };\n\n    // Connection state change\n    pc.onconnectionstatechange = () => {\n      console.log('Connection state:', pc.connectionState);\n\n      if (pc.connectionState === 'connected') {\n        setIsConnected(true);\n        setConnectionStatus('Bağlantı kuruldu');\n        setRetryCount(0); // Başarılı bağlantıda retry sayacını sıfırla\n\n        // Retry timeout'unu temizle\n        if (retryTimeoutRef.current) {\n          clearTimeout(retryTimeoutRef.current);\n          retryTimeoutRef.current = null;\n        }\n\n      } else if (pc.connectionState === 'disconnected') {\n        setIsConnected(false);\n        setConnectionStatus('Bağlantı kesildi');\n\n      } else if (pc.connectionState === 'failed') {\n        console.log('❌ MEETING: WebRTC bağlantısı başarısız oldu');\n        setIsConnected(false);\n        setConnectionStatus('Bağlantı başarısız, yeniden deneniyor...');\n\n        // Otomatik yeniden deneme\n        handleConnectionFailure();\n      }\n    };\n\n    // Local stream'i peer connection'a ekle\n    if (localStreamRef.current) {\n      localStreamRef.current.getTracks().forEach(track => {\n        pc.addTrack(track, localStreamRef.current);\n      });\n    }\n  };\n\n  const createOffer = async () => {\n    try {\n      const pc = peerConnectionRef.current;\n\n      if (!pc) {\n        console.error('❌ MEETING: Peer connection bulunamadı');\n        return;\n      }\n\n      if (pc.signalingState !== 'stable') {\n        console.log(`⚠️ MEETING: Yanlış signaling state: ${pc.signalingState}, offer atlanıyor`);\n        return;\n      }\n\n      console.log('📤 MEETING: Offer oluşturuluyor...');\n      const offer = await pc.createOffer({\n        offerToReceiveAudio: true,\n        offerToReceiveVideo: true\n      });\n\n      console.log('🔄 MEETING: Local description ayarlanıyor...');\n      await pc.setLocalDescription(offer);\n\n      console.log('📤 MEETING: Offer gönderiliyor...');\n      socketRef.current.emit('offer', {\n        roomId: `appointment-${appointmentId}`,\n        sdp: offer\n      });\n    } catch (error) {\n      console.error('❌ MEETING: Offer oluşturma hatası:', error);\n    }\n  };\n\n  const handleOffer = async (sdp) => {\n    try {\n      console.log('📥 MEETING: Offer alındı, işleniyor...');\n\n      // Peer connection yoksa oluştur\n      if (!peerConnectionRef.current) {\n        createPeerConnection();\n      }\n\n      const pc = peerConnectionRef.current;\n\n      // State kontrolü\n      if (pc.signalingState !== 'stable' && pc.signalingState !== 'have-local-offer') {\n        console.log(`⚠️ MEETING: Yanlış signaling state: ${pc.signalingState}, offer atlanıyor`);\n        return;\n      }\n\n      console.log('🔄 MEETING: Remote description ayarlanıyor...');\n      await pc.setRemoteDescription(new RTCSessionDescription(sdp));\n\n      console.log('📤 MEETING: Answer oluşturuluyor...');\n      const answer = await pc.createAnswer();\n      await pc.setLocalDescription(answer);\n\n      console.log('📤 MEETING: Answer gönderiliyor...');\n      socketRef.current.emit('answer', {\n        roomId: `appointment-${appointmentId}`,\n        sdp: answer\n      });\n    } catch (error) {\n      console.error('❌ MEETING: Offer işleme hatası:', error);\n    }\n  };\n\n  const handleAnswer = async (sdp) => {\n    try {\n      console.log('📥 MEETING: Answer alındı, işleniyor...');\n      const pc = peerConnectionRef.current;\n\n      if (!pc) {\n        console.error('❌ MEETING: Peer connection bulunamadı');\n        return;\n      }\n\n      // State kontrolü\n      if (pc.signalingState !== 'have-local-offer') {\n        console.log(`⚠️ MEETING: Yanlış signaling state: ${pc.signalingState}, answer atlanıyor`);\n        return;\n      }\n\n      console.log('🔄 MEETING: Remote description (answer) ayarlanıyor...');\n      await pc.setRemoteDescription(new RTCSessionDescription(sdp));\n      console.log('✅ MEETING: Answer başarıyla işlendi');\n    } catch (error) {\n      console.error('❌ MEETING: Answer işleme hatası:', error);\n    }\n  };\n\n  const handleCandidate = async (candidate) => {\n    try {\n      const pc = peerConnectionRef.current;\n      if (!pc) {\n        console.log('⚠️ MEETING: Peer connection yok, ICE candidate atlanıyor');\n        return;\n      }\n\n      if (pc.remoteDescription) {\n        console.log('🧊 MEETING: ICE candidate ekleniyor...');\n        await pc.addIceCandidate(new RTCIceCandidate(candidate));\n        console.log('✅ MEETING: ICE candidate başarıyla eklendi');\n      } else {\n        console.log('⚠️ MEETING: Remote description yok, ICE candidate atlanıyor');\n      }\n    } catch (error) {\n      console.error('❌ MEETING: ICE candidate işleme hatası:', error);\n    }\n  };\n\n  const toggleAudio = () => {\n    if (localStreamRef.current) {\n      const audioTrack = localStreamRef.current.getAudioTracks()[0];\n      if (audioTrack) {\n        audioTrack.enabled = !audioTrack.enabled;\n        setIsAudioMuted(!audioTrack.enabled);\n      }\n    }\n  };\n\n  const toggleVideo = () => {\n    if (localStreamRef.current) {\n      const videoTrack = localStreamRef.current.getVideoTracks()[0];\n      if (videoTrack) {\n        videoTrack.enabled = !videoTrack.enabled;\n        setIsVideoMuted(!videoTrack.enabled);\n      }\n    }\n  };\n\n  const toggleSpeaker = () => {\n    if (remoteVideoRef.current) {\n      remoteVideoRef.current.muted = isSpeakerOn;\n      setIsSpeakerOn(!isSpeakerOn);\n    }\n  };\n\n  const endCall = () => {\n    cleanup();\n\n    // Kullanıcının rolüne göre doğru sayfaya yönlendir\n    if (user.role?.name === 'Admin') {\n      navigate('/admin/dashboard');\n    } else if (user.role?.name === 'Expert') {\n      navigate('/expert/sessions');\n    } else if (user.role?.name === 'Client') {\n      navigate('/client/sessions');\n    } else {\n      navigate('/');\n    }\n\n    toast.success('Görüşme sonlandırıldı');\n  };\n\n  const cleanup = () => {\n    console.log('🧹 MEETING: Cleanup başlatılıyor...');\n\n    // Socket bağlantısını kapat\n    if (socketRef.current) {\n      console.log('🔌 MEETING: Socket bağlantısı kapatılıyor...');\n\n      try {\n        // Sadece bağlıysa leave event'i gönder\n        if (socketRef.current.connected) {\n          socketRef.current.emit('leave', `appointment-${appointmentId}`);\n        }\n\n        // Event listener'ları kaldır\n        socketRef.current.off();\n\n        // Bağlantıyı kapat\n        socketRef.current.disconnect();\n      } catch (error) {\n        console.error('❌ MEETING: Socket cleanup hatası:', error);\n      } finally {\n        socketRef.current = null;\n      }\n    }\n\n    // Peer connection'ı kapat\n    if (peerConnectionRef.current) {\n      console.log('🔗 MEETING: Peer connection kapatılıyor...');\n      peerConnectionRef.current.onicecandidate = null;\n      peerConnectionRef.current.ontrack = null;\n      peerConnectionRef.current.close();\n      peerConnectionRef.current = null;\n    }\n\n    // Local stream'i durdur\n    if (localStreamRef.current) {\n      console.log('📹 MEETING: Local stream durduruluyor...');\n      localStreamRef.current.getTracks().forEach(track => {\n        track.stop();\n        console.log(`📹 MEETING: Track durduruldu: ${track.kind}`);\n      });\n      localStreamRef.current = null;\n    }\n\n    // Video elementlerini temizle\n    if (localVideoRef.current) {\n      localVideoRef.current.srcObject = null;\n    }\n    if (remoteVideoRef.current) {\n      remoteVideoRef.current.srcObject = null;\n    }\n\n    // State'leri sıfırla\n    setIsConnected(false);\n    setParticipantCount(0);\n    setConnectionStatus('Bağlantı kesildi');\n    setIsInitiator(false);\n    hasJoinedRoom.current = false;\n\n    console.log('✅ MEETING: Cleanup tamamlandı');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 text-white\">\n      {/* Header */}\n      <div className=\"bg-gray-800 px-6 py-4 border-b border-gray-700\">\n        <div className=\"flex justify-between items-center\">\n          <div>\n            <h1 className=\"text-xl font-semibold\">Psikolojik Danışmanlık Seansı</h1>\n            <p className=\"text-sm text-gray-400\">Randevu #{appointmentId}</p>\n          </div>\n          <div className=\"text-right\">\n            <p className=\"text-sm text-gray-400\">{connectionStatus}</p>\n            <p className=\"text-xs text-gray-500\">{participantCount} katılımcı</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Video Container */}\n      <div className=\"flex-1 p-6\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 h-[calc(100vh-200px)]\">\n          {/* Local Video */}\n          <div className=\"relative bg-gray-800 rounded-lg overflow-hidden\">\n            <video\n              ref={localVideoRef}\n              autoPlay\n              playsInline\n              muted\n              className=\"w-full h-full object-cover\"\n            />\n            <div className=\"absolute bottom-4 left-4 bg-black bg-opacity-50 px-3 py-1 rounded\">\n              <span className=\"text-sm\">Siz</span>\n            </div>\n            {isVideoMuted && (\n              <div className=\"absolute inset-0 bg-gray-700 flex items-center justify-center\">\n                <VideoCameraIconSolid className=\"h-16 w-16 text-gray-400\" />\n              </div>\n            )}\n          </div>\n\n          {/* Remote Video */}\n          <div className=\"relative bg-gray-800 rounded-lg overflow-hidden\">\n            <video\n              ref={remoteVideoRef}\n              autoPlay\n              playsInline\n              className=\"w-full h-full object-cover\"\n            />\n            <div className=\"absolute bottom-4 left-4 bg-black bg-opacity-50 px-3 py-1 rounded\">\n              <span className=\"text-sm\">Karşı Taraf</span>\n            </div>\n            {!isConnected && (\n              <div className=\"absolute inset-0 bg-gray-700 flex items-center justify-center\">\n                <div className=\"text-center\">\n                  <VideoCameraIconSolid className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n                  <p className=\"text-gray-400\">Karşı taraf bekleniyor...</p>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Controls */}\n      <div className=\"fixed bottom-6 left-1/2 transform -translate-x-1/2\">\n        <div className=\"flex space-x-4 bg-gray-800 rounded-full px-6 py-3 shadow-lg\">\n          {/* Mikrofon */}\n          <button\n            onClick={toggleAudio}\n            className={`p-3 rounded-full transition-colors ${\n              isAudioMuted \n                ? 'bg-red-600 hover:bg-red-700' \n                : 'bg-gray-600 hover:bg-gray-700'\n            }`}\n          >\n            {isAudioMuted ? (\n              <MicrophoneIconSolid className=\"h-6 w-6\" />\n            ) : (\n              <MicrophoneIcon className=\"h-6 w-6\" />\n            )}\n          </button>\n\n          {/* Kamera */}\n          <button\n            onClick={toggleVideo}\n            className={`p-3 rounded-full transition-colors ${\n              isVideoMuted \n                ? 'bg-red-600 hover:bg-red-700' \n                : 'bg-gray-600 hover:bg-gray-700'\n            }`}\n          >\n            {isVideoMuted ? (\n              <VideoCameraIconSolid className=\"h-6 w-6\" />\n            ) : (\n              <VideoCameraIcon className=\"h-6 w-6\" />\n            )}\n          </button>\n\n          {/* Hoparlör */}\n          <button\n            onClick={toggleSpeaker}\n            className={`p-3 rounded-full transition-colors ${\n              !isSpeakerOn \n                ? 'bg-red-600 hover:bg-red-700' \n                : 'bg-gray-600 hover:bg-gray-700'\n            }`}\n          >\n            {isSpeakerOn ? (\n              <SpeakerWaveIcon className=\"h-6 w-6\" />\n            ) : (\n              <SpeakerWaveIconSolid className=\"h-6 w-6\" />\n            )}\n          </button>\n\n          {/* Görüşmeyi Sonlandır */}\n          <button\n            onClick={endCall}\n            className=\"p-3 rounded-full bg-red-600 hover:bg-red-700 transition-colors\"\n          >\n            <PhoneXMarkIcon className=\"h-6 w-6\" />\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default MeetingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SACEC,cAAc,EACdC,eAAe,EACfC,cAAc,EACdC,eAAe,QACV,6BAA6B;AACpC,SACEH,cAAc,IAAII,mBAAmB,EACrCH,eAAe,IAAII,oBAAoB,EACvCF,eAAe,IAAIG,oBAAoB,QAClC,2BAA2B;AAClC,OAAOC,EAAE,MAAM,kBAAkB;AACjC,OAAOC,KAAK,MAAM,iBAAiB;AACnC,SAASC,OAAO,QAAQ,qBAAqB;;AAE7C;AACA;AACA;AAFA,SAAAC,MAAA,IAAAC,OAAA;AAGA,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC;EAAc,CAAC,GAAGhB,SAAS,CAAC,CAAC;EACrC,MAAMiB,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEiB,IAAI;IAAEC;EAAc,CAAC,GAAGR,OAAO,CAAC,CAAC;;EAEzC;EACAb,SAAS,CAAC,MAAM;IAAA,IAAAsB,UAAA;IACdC,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEN,aAAa,CAAC;IACzEK,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEJ,IAAI,GAAG;MAAEK,EAAE,EAAEL,IAAI,CAACK,EAAE;MAAEC,IAAI,GAAAJ,UAAA,GAAEF,IAAI,CAACM,IAAI,cAAAJ,UAAA,uBAATA,UAAA,CAAWK;IAAK,CAAC,GAAG,eAAe,CAAC;EAC1F,CAAC,EAAE,CAACP,IAAI,EAAEF,aAAa,CAAC,CAAC;;EAEzB;EACA,MAAM,CAACU,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACqC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtC,QAAQ,CAAC,eAAe,CAAC;EACzE,MAAM,CAACuC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxC,QAAQ,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACyC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC2C,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC,CAAC,CAAC;;EAE/C;EACA,MAAM6C,aAAa,GAAG3C,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM4C,cAAc,GAAG5C,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM6C,SAAS,GAAG7C,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM8C,iBAAiB,GAAG9C,MAAM,CAAC,IAAI,CAAC;EACtC,MAAM+C,cAAc,GAAG/C,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMgD,aAAa,GAAGhD,MAAM,CAAC,KAAK,CAAC;EACnC,MAAMiD,eAAe,GAAGjD,MAAM,CAAC,IAAI,CAAC;;EAEpC;EACA,MAAMkD,SAAS,GAAG;IAChBC,UAAU,EAAE,CACV;MAAEC,IAAI,EAAE;IAA+B,CAAC,EACxC;MAAEA,IAAI,EAAE;IAAgC,CAAC;EAE7C,CAAC;EAEDrD,SAAS,CAAC,MAAM;IAAA,IAAAsD,kBAAA;IACd,IAAIC,SAAS,GAAG,IAAI;;IAEpB;IACA,IAAInC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEK,EAAE,IAAIP,aAAa,IAAI,GAAAoC,kBAAA,GAACR,SAAS,CAACU,OAAO,cAAAF,kBAAA,eAAjBA,kBAAA,CAAmBG,SAAS,GAAE;MAC9DlC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE;QAAEkC,MAAM,EAAEtC,IAAI,CAACK,EAAE;QAAEP;MAAc,CAAC,CAAC;;MAEvF;MACA,MAAMyC,cAAc,GAAG,MAAAA,CAAA,KAAY;QACjC,IAAIJ,SAAS,EAAE;UACb,MAAMK,oBAAoB,CAAC,CAAC;QAC9B;MACF,CAAC;MAEDD,cAAc,CAAC,CAAC;IAClB;IAEA,OAAO,MAAM;MACXJ,SAAS,GAAG,KAAK;MACjBhC,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;;MAEnE;MACAqC,UAAU,CAAC,MAAM;QACf,IAAI,CAACN,SAAS,EAAE;UACdO,OAAO,CAAC,CAAC;QACX;MACF,CAAC,EAAE,GAAG,CAAC;IACT,CAAC;EACH,CAAC,EAAE,CAAC1C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,EAAE,EAAEP,aAAa,CAAC,CAAC;EAE7B,MAAM0C,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MAAA,IAAAG,mBAAA,EAAAC,mBAAA;MACF;MACA,KAAAD,mBAAA,GAAIjB,SAAS,CAACU,OAAO,cAAAO,mBAAA,eAAjBA,mBAAA,CAAmBN,SAAS,EAAE;QAChClC,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;QAC3E;MACF;;MAEA;MACA,MAAMyC,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,4BAA4B;MAC/E7C,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEyC,SAAS,CAAC;;MAErE;MACA,MAAMI,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MAEjDzB,SAAS,CAACU,OAAO,GAAG7C,EAAE,CAACsD,SAAS,EAAE;QAChCO,IAAI,EAAE;UAAEH;QAAM,CAAC;QACfI,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;QACpCC,OAAO,EAAE,KAAK;QACdC,YAAY,EAAE,IAAI;QAClBC,iBAAiB,EAAE,IAAI;QACvBC,oBAAoB,EAAE,CAAC;QACvBC,QAAQ,EAAE,KAAK;QAAE;QACjBC,kBAAkB,EAAE,KAAK;QAAE;QAC3BC,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE,IAAI;QACbC,eAAe,EAAE;MACnB,CAAC,CAAC;;MAEF;MACAC,oBAAoB,CAAC,CAAC;;MAEtB;MACA,MAAMC,YAAY,CAAC,CAAC;;MAEpB;MACA,IAAI,CAACnC,aAAa,CAACO,OAAO,KAAAQ,mBAAA,GAAIlB,SAAS,CAACU,OAAO,cAAAQ,mBAAA,eAAjBA,mBAAA,CAAmBP,SAAS,EAAE;QAC1D,MAAM4B,MAAM,GAAG,eAAenE,aAAa,EAAE;QAC7CK,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE6D,MAAM,CAAC;QACrEvC,SAAS,CAACU,OAAO,CAAC8B,IAAI,CAAC,MAAM,EAAED,MAAM,CAAC;QACtCpC,aAAa,CAACO,OAAO,GAAG,IAAI;MAC9B,CAAC,MAAM,IAAIP,aAAa,CAACO,OAAO,EAAE;QAChCjC,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC;MACnF,CAAC,MAAM;QACLD,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC5E;IAEF,CAAC,CAAC,OAAO+D,KAAK,EAAE;MACdhE,OAAO,CAACgE,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxC3E,KAAK,CAAC2E,KAAK,CAAC,qBAAqB,CAAC;MAClClD,mBAAmB,CAAC,iBAAiB,CAAC;IACxC;EACF,CAAC;EAED,MAAM8C,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMK,MAAM,GAAG1C,SAAS,CAACU,OAAO;;IAEhC;IACAgC,MAAM,CAACC,EAAE,CAAC,SAAS,EAAE,MAAM;MACzBlE,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEgE,MAAM,CAAC/D,EAAE,CAAC;MAClEY,mBAAmB,CAAC,8BAA8B,CAAC;IACrD,CAAC,CAAC;IAEFmD,MAAM,CAACC,EAAE,CAAC,YAAY,EAAGC,MAAM,IAAK;MAClCnE,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEkE,MAAM,CAAC;MAC/DrD,mBAAmB,CAAC,kBAAkB,CAAC;MACvCR,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,CAAC;IAEF2D,MAAM,CAACC,EAAE,CAAC,eAAe,EAAGF,KAAK,IAAK;MACpChE,OAAO,CAACgE,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9DlD,mBAAmB,CAAC,iBAAiB,CAAC;IACxC,CAAC,CAAC;IAEFmD,MAAM,CAACC,EAAE,CAAC,QAAQ,EAAE,CAAC;MAAEJ,MAAM;MAAEM;IAAS,CAAC,KAAK;MAC5CpE,OAAO,CAACC,GAAG,CAAC,6BAA6B6D,MAAM,uBAAuBM,QAAQ,EAAE,CAAC;MACjFpD,mBAAmB,CAACoD,QAAQ,CAAC;MAC7BtD,mBAAmB,CAAC,SAASsD,QAAQ,OAAO,CAAC;MAE7C,IAAIA,QAAQ,KAAK,CAAC,EAAE;QAClBtD,mBAAmB,CAAC,4BAA4B,CAAC;QACjDI,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;QACvBlB,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;MAC3D,CAAC,MAAM,IAAImE,QAAQ,KAAK,CAAC,EAAE;QACzBlD,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;QACtBlB,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAC7D;IACF,CAAC,CAAC;IAEFgE,MAAM,CAACC,EAAE,CAAC,OAAO,EAAE,CAAC;MAAEjD;IAAY,CAAC,KAAK;MACtCjB,OAAO,CAACC,GAAG,CAAC,sEAAsEgB,WAAW,EAAE,CAAC;MAChGH,mBAAmB,CAAC,uBAAuB,CAAC;MAC5CI,cAAc,CAACD,WAAW,CAAC;;MAE3B;MACAoD,oBAAoB,CAAC,CAAC;;MAEtB;MACA/B,UAAU,CAAC,MAAM;QACf,IAAIrB,WAAW,EAAE;UACfjB,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;UACjEqE,WAAW,CAAC,CAAC;QACf,CAAC,MAAM;UACLtE,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC/C;MACF,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;IAEFgE,MAAM,CAACC,EAAE,CAAC,MAAM,EAAGJ,MAAM,IAAK;MAC5B9D,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE6D,MAAM,CAAC;MAChCzE,KAAK,CAAC2E,KAAK,CAAC,oBAAoB,CAAC;MACjClD,mBAAmB,CAAC,UAAU,CAAC;IACjC,CAAC,CAAC;IAEFmD,MAAM,CAACC,EAAE,CAAC,OAAO,EAAE,MAAOK,GAAG,IAAK;MAChCvE,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;MAC3B,MAAMuE,WAAW,CAACD,GAAG,CAAC;IACxB,CAAC,CAAC;IAEFN,MAAM,CAACC,EAAE,CAAC,QAAQ,EAAE,MAAOK,GAAG,IAAK;MACjCvE,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B,MAAMwE,YAAY,CAACF,GAAG,CAAC;IACzB,CAAC,CAAC;IAEFN,MAAM,CAACC,EAAE,CAAC,WAAW,EAAE,MAAOQ,SAAS,IAAK;MAC1C1E,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnC,MAAM0E,eAAe,CAACD,SAAS,CAAC;IAClC,CAAC,CAAC;IAEFT,MAAM,CAACC,EAAE,CAAC,OAAO,EAAE,MAAM;MACvBlE,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MAClCZ,KAAK,CAAC,gCAAgC,EAAE;QAAEuF,IAAI,EAAE;MAAK,CAAC,CAAC;MACvD9D,mBAAmB,CAAC,qBAAqB,CAAC;MAC1CE,mBAAmB,CAAC,CAAC,CAAC;;MAEtB;MACA,IAAIM,cAAc,CAACW,OAAO,EAAE;QAC1BX,cAAc,CAACW,OAAO,CAAC4C,SAAS,GAAG,IAAI;MACzC;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMhB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF;MACA,IAAIpC,cAAc,CAACQ,OAAO,EAAE;QAC1BjC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpD,OAAOwB,cAAc,CAACQ,OAAO;MAC/B;MAEAjC,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;MACzD,MAAM6E,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACnB,YAAY,CAAC;QACvDoB,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE;MACT,CAAC,CAAC;MAEFzD,cAAc,CAACQ,OAAO,GAAG6C,MAAM;MAC/B,IAAIzD,aAAa,CAACY,OAAO,EAAE;QACzBZ,aAAa,CAACY,OAAO,CAAC4C,SAAS,GAAGC,MAAM;MAC1C;MAEA9E,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClD,OAAO6E,MAAM;IACf,CAAC,CAAC,OAAOd,KAAK,EAAE;MACdhE,OAAO,CAACgE,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD3E,KAAK,CAAC2E,KAAK,CAAC,oCAAoC,CAAC;MACjD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMK,oBAAoB,GAAGA,CAAA,KAAM;IACjC;IACA,IAAI7C,iBAAiB,CAACS,OAAO,EAAE;MAC7BjC,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;MAC9E,OAAOuB,iBAAiB,CAACS,OAAO;IAClC;IAEAjC,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAChE,MAAMkF,EAAE,GAAG,IAAIC,iBAAiB,CAACxD,SAAS,CAAC;IAC3CJ,iBAAiB,CAACS,OAAO,GAAGkD,EAAE;;IAE9B;IACAA,EAAE,CAACE,cAAc,GAAIC,KAAK,IAAK;MAC7B,IAAIA,KAAK,CAACZ,SAAS,EAAE;QACnBnD,SAAS,CAACU,OAAO,CAAC8B,IAAI,CAAC,WAAW,EAAE;UAClCD,MAAM,EAAE,eAAenE,aAAa,EAAE;UACtC+E,SAAS,EAAEY,KAAK,CAACZ;QACnB,CAAC,CAAC;MACJ;IACF,CAAC;;IAED;IACAS,EAAE,CAACI,OAAO,GAAID,KAAK,IAAK;MACtBtF,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnC,MAAM,CAACuF,YAAY,CAAC,GAAGF,KAAK,CAACG,OAAO;MACpC,IAAInE,cAAc,CAACW,OAAO,EAAE;QAC1BX,cAAc,CAACW,OAAO,CAAC4C,SAAS,GAAGW,YAAY;MACjD;MACAlF,cAAc,CAAC,IAAI,CAAC;MACpBQ,mBAAmB,CAAC,kBAAkB,CAAC;IACzC,CAAC;;IAED;IACAqE,EAAE,CAACO,uBAAuB,GAAG,MAAM;MACjC1F,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEkF,EAAE,CAACQ,eAAe,CAAC;MAEpD,IAAIR,EAAE,CAACQ,eAAe,KAAK,WAAW,EAAE;QACtCrF,cAAc,CAAC,IAAI,CAAC;QACpBQ,mBAAmB,CAAC,kBAAkB,CAAC;QACvCM,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;;QAElB;QACA,IAAIO,eAAe,CAACM,OAAO,EAAE;UAC3B2D,YAAY,CAACjE,eAAe,CAACM,OAAO,CAAC;UACrCN,eAAe,CAACM,OAAO,GAAG,IAAI;QAChC;MAEF,CAAC,MAAM,IAAIkD,EAAE,CAACQ,eAAe,KAAK,cAAc,EAAE;QAChDrF,cAAc,CAAC,KAAK,CAAC;QACrBQ,mBAAmB,CAAC,kBAAkB,CAAC;MAEzC,CAAC,MAAM,IAAIqE,EAAE,CAACQ,eAAe,KAAK,QAAQ,EAAE;QAC1C3F,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;QAC1DK,cAAc,CAAC,KAAK,CAAC;QACrBQ,mBAAmB,CAAC,0CAA0C,CAAC;;QAE/D;QACA+E,uBAAuB,CAAC,CAAC;MAC3B;IACF,CAAC;;IAED;IACA,IAAIpE,cAAc,CAACQ,OAAO,EAAE;MAC1BR,cAAc,CAACQ,OAAO,CAAC6D,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAI;QAClDb,EAAE,CAACc,QAAQ,CAACD,KAAK,EAAEvE,cAAc,CAACQ,OAAO,CAAC;MAC5C,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMqC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMa,EAAE,GAAG3D,iBAAiB,CAACS,OAAO;MAEpC,IAAI,CAACkD,EAAE,EAAE;QACPnF,OAAO,CAACgE,KAAK,CAAC,uCAAuC,CAAC;QACtD;MACF;MAEA,IAAImB,EAAE,CAACe,cAAc,KAAK,QAAQ,EAAE;QAClClG,OAAO,CAACC,GAAG,CAAC,uCAAuCkF,EAAE,CAACe,cAAc,mBAAmB,CAAC;QACxF;MACF;MAEAlG,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjD,MAAMkG,KAAK,GAAG,MAAMhB,EAAE,CAACb,WAAW,CAAC;QACjC8B,mBAAmB,EAAE,IAAI;QACzBC,mBAAmB,EAAE;MACvB,CAAC,CAAC;MAEFrG,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAC3D,MAAMkF,EAAE,CAACmB,mBAAmB,CAACH,KAAK,CAAC;MAEnCnG,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChDsB,SAAS,CAACU,OAAO,CAAC8B,IAAI,CAAC,OAAO,EAAE;QAC9BD,MAAM,EAAE,eAAenE,aAAa,EAAE;QACtC4E,GAAG,EAAE4B;MACP,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOnC,KAAK,EAAE;MACdhE,OAAO,CAACgE,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC5D;EACF,CAAC;EAED,MAAMQ,WAAW,GAAG,MAAOD,GAAG,IAAK;IACjC,IAAI;MACFvE,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;MAErD;MACA,IAAI,CAACuB,iBAAiB,CAACS,OAAO,EAAE;QAC9BoC,oBAAoB,CAAC,CAAC;MACxB;MAEA,MAAMc,EAAE,GAAG3D,iBAAiB,CAACS,OAAO;;MAEpC;MACA,IAAIkD,EAAE,CAACe,cAAc,KAAK,QAAQ,IAAIf,EAAE,CAACe,cAAc,KAAK,kBAAkB,EAAE;QAC9ElG,OAAO,CAACC,GAAG,CAAC,uCAAuCkF,EAAE,CAACe,cAAc,mBAAmB,CAAC;QACxF;MACF;MAEAlG,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5D,MAAMkF,EAAE,CAACoB,oBAAoB,CAAC,IAAIC,qBAAqB,CAACjC,GAAG,CAAC,CAAC;MAE7DvE,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClD,MAAMwG,MAAM,GAAG,MAAMtB,EAAE,CAACuB,YAAY,CAAC,CAAC;MACtC,MAAMvB,EAAE,CAACmB,mBAAmB,CAACG,MAAM,CAAC;MAEpCzG,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjDsB,SAAS,CAACU,OAAO,CAAC8B,IAAI,CAAC,QAAQ,EAAE;QAC/BD,MAAM,EAAE,eAAenE,aAAa,EAAE;QACtC4E,GAAG,EAAEkC;MACP,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOzC,KAAK,EAAE;MACdhE,OAAO,CAACgE,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD;EACF,CAAC;EAED,MAAMS,YAAY,GAAG,MAAOF,GAAG,IAAK;IAClC,IAAI;MACFvE,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD,MAAMkF,EAAE,GAAG3D,iBAAiB,CAACS,OAAO;MAEpC,IAAI,CAACkD,EAAE,EAAE;QACPnF,OAAO,CAACgE,KAAK,CAAC,uCAAuC,CAAC;QACtD;MACF;;MAEA;MACA,IAAImB,EAAE,CAACe,cAAc,KAAK,kBAAkB,EAAE;QAC5ClG,OAAO,CAACC,GAAG,CAAC,uCAAuCkF,EAAE,CAACe,cAAc,oBAAoB,CAAC;QACzF;MACF;MAEAlG,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;MACrE,MAAMkF,EAAE,CAACoB,oBAAoB,CAAC,IAAIC,qBAAqB,CAACjC,GAAG,CAAC,CAAC;MAC7DvE,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IACpD,CAAC,CAAC,OAAO+D,KAAK,EAAE;MACdhE,OAAO,CAACgE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAC1D;EACF,CAAC;EAED,MAAMW,eAAe,GAAG,MAAOD,SAAS,IAAK;IAC3C,IAAI;MACF,MAAMS,EAAE,GAAG3D,iBAAiB,CAACS,OAAO;MACpC,IAAI,CAACkD,EAAE,EAAE;QACPnF,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;QACvE;MACF;MAEA,IAAIkF,EAAE,CAACwB,iBAAiB,EAAE;QACxB3G,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrD,MAAMkF,EAAE,CAACyB,eAAe,CAAC,IAAIC,eAAe,CAACnC,SAAS,CAAC,CAAC;QACxD1E,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;MAC3D,CAAC,MAAM;QACLD,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC5E;IACF,CAAC,CAAC,OAAO+D,KAAK,EAAE;MACdhE,OAAO,CAACgE,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IACjE;EACF,CAAC;EAED,MAAM8C,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIrF,cAAc,CAACQ,OAAO,EAAE;MAC1B,MAAM8E,UAAU,GAAGtF,cAAc,CAACQ,OAAO,CAAC+E,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7D,IAAID,UAAU,EAAE;QACdA,UAAU,CAACE,OAAO,GAAG,CAACF,UAAU,CAACE,OAAO;QACxCzG,eAAe,CAAC,CAACuG,UAAU,CAACE,OAAO,CAAC;MACtC;IACF;EACF,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIzF,cAAc,CAACQ,OAAO,EAAE;MAC1B,MAAMkF,UAAU,GAAG1F,cAAc,CAACQ,OAAO,CAACmF,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7D,IAAID,UAAU,EAAE;QACdA,UAAU,CAACF,OAAO,GAAG,CAACE,UAAU,CAACF,OAAO;QACxCvG,eAAe,CAAC,CAACyG,UAAU,CAACF,OAAO,CAAC;MACtC;IACF;EACF,CAAC;EAED,MAAMI,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI/F,cAAc,CAACW,OAAO,EAAE;MAC1BX,cAAc,CAACW,OAAO,CAACqF,KAAK,GAAG3G,WAAW;MAC1CC,cAAc,CAAC,CAACD,WAAW,CAAC;IAC9B;EACF,CAAC;EAED,MAAM4G,OAAO,GAAGA,CAAA,KAAM;IAAA,IAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA;IACpBnF,OAAO,CAAC,CAAC;;IAET;IACA,IAAI,EAAAiF,WAAA,GAAA3H,IAAI,CAACM,IAAI,cAAAqH,WAAA,uBAATA,WAAA,CAAWpH,IAAI,MAAK,OAAO,EAAE;MAC/BR,QAAQ,CAAC,kBAAkB,CAAC;IAC9B,CAAC,MAAM,IAAI,EAAA6H,WAAA,GAAA5H,IAAI,CAACM,IAAI,cAAAsH,WAAA,uBAATA,WAAA,CAAWrH,IAAI,MAAK,QAAQ,EAAE;MACvCR,QAAQ,CAAC,kBAAkB,CAAC;IAC9B,CAAC,MAAM,IAAI,EAAA8H,WAAA,GAAA7H,IAAI,CAACM,IAAI,cAAAuH,WAAA,uBAATA,WAAA,CAAWtH,IAAI,MAAK,QAAQ,EAAE;MACvCR,QAAQ,CAAC,kBAAkB,CAAC;IAC9B,CAAC,MAAM;MACLA,QAAQ,CAAC,GAAG,CAAC;IACf;IAEAP,KAAK,CAACsI,OAAO,CAAC,uBAAuB,CAAC;EACxC,CAAC;EAED,MAAMpF,OAAO,GAAGA,CAAA,KAAM;IACpBvC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;;IAElD;IACA,IAAIsB,SAAS,CAACU,OAAO,EAAE;MACrBjC,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAE3D,IAAI;QACF;QACA,IAAIsB,SAAS,CAACU,OAAO,CAACC,SAAS,EAAE;UAC/BX,SAAS,CAACU,OAAO,CAAC8B,IAAI,CAAC,OAAO,EAAE,eAAepE,aAAa,EAAE,CAAC;QACjE;;QAEA;QACA4B,SAAS,CAACU,OAAO,CAAC2F,GAAG,CAAC,CAAC;;QAEvB;QACArG,SAAS,CAACU,OAAO,CAAC4F,UAAU,CAAC,CAAC;MAChC,CAAC,CAAC,OAAO7D,KAAK,EAAE;QACdhE,OAAO,CAACgE,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAC3D,CAAC,SAAS;QACRzC,SAAS,CAACU,OAAO,GAAG,IAAI;MAC1B;IACF;;IAEA;IACA,IAAIT,iBAAiB,CAACS,OAAO,EAAE;MAC7BjC,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;MACzDuB,iBAAiB,CAACS,OAAO,CAACoD,cAAc,GAAG,IAAI;MAC/C7D,iBAAiB,CAACS,OAAO,CAACsD,OAAO,GAAG,IAAI;MACxC/D,iBAAiB,CAACS,OAAO,CAAC6F,KAAK,CAAC,CAAC;MACjCtG,iBAAiB,CAACS,OAAO,GAAG,IAAI;IAClC;;IAEA;IACA,IAAIR,cAAc,CAACQ,OAAO,EAAE;MAC1BjC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACvDwB,cAAc,CAACQ,OAAO,CAAC6D,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAI;QAClDA,KAAK,CAAC+B,IAAI,CAAC,CAAC;QACZ/H,OAAO,CAACC,GAAG,CAAC,iCAAiC+F,KAAK,CAACgC,IAAI,EAAE,CAAC;MAC5D,CAAC,CAAC;MACFvG,cAAc,CAACQ,OAAO,GAAG,IAAI;IAC/B;;IAEA;IACA,IAAIZ,aAAa,CAACY,OAAO,EAAE;MACzBZ,aAAa,CAACY,OAAO,CAAC4C,SAAS,GAAG,IAAI;IACxC;IACA,IAAIvD,cAAc,CAACW,OAAO,EAAE;MAC1BX,cAAc,CAACW,OAAO,CAAC4C,SAAS,GAAG,IAAI;IACzC;;IAEA;IACAvE,cAAc,CAAC,KAAK,CAAC;IACrBU,mBAAmB,CAAC,CAAC,CAAC;IACtBF,mBAAmB,CAAC,kBAAkB,CAAC;IACvCI,cAAc,CAAC,KAAK,CAAC;IACrBQ,aAAa,CAACO,OAAO,GAAG,KAAK;IAE7BjC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;EAC9C,CAAC;EAED,oBACET,OAAA;IAAKyI,SAAS,EAAC,qCAAqC;IAAAC,QAAA,gBAElD1I,OAAA;MAAKyI,SAAS,EAAC,gDAAgD;MAAAC,QAAA,eAC7D1I,OAAA;QAAKyI,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChD1I,OAAA;UAAA0I,QAAA,gBACE1I,OAAA;YAAIyI,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxE9I,OAAA;YAAGyI,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,WAAS,EAACvI,aAAa;UAAA;YAAAwI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACN9I,OAAA;UAAKyI,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB1I,OAAA;YAAGyI,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAErH;UAAgB;YAAAsH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3D9I,OAAA;YAAGyI,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAEnH,gBAAgB,EAAC,2BAAU;UAAA;YAAAoH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9I,OAAA;MAAKyI,SAAS,EAAC,YAAY;MAAAC,QAAA,eACzB1I,OAAA;QAAKyI,SAAS,EAAC,6DAA6D;QAAAC,QAAA,gBAE1E1I,OAAA;UAAKyI,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAC9D1I,OAAA;YACE+I,GAAG,EAAElH,aAAc;YACnBmH,QAAQ;YACRC,WAAW;YACXnB,KAAK;YACLW,SAAS,EAAC;UAA4B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACF9I,OAAA;YAAKyI,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChF1I,OAAA;cAAMyI,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,EACL7H,YAAY,iBACXjB,OAAA;YAAKyI,SAAS,EAAC,+DAA+D;YAAAC,QAAA,eAC5E1I,OAAA,CAACN,oBAAoB;cAAC+I,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN9I,OAAA;UAAKyI,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAC9D1I,OAAA;YACE+I,GAAG,EAAEjH,cAAe;YACpBkH,QAAQ;YACRC,WAAW;YACXR,SAAS,EAAC;UAA4B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACF9I,OAAA;YAAKyI,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChF1I,OAAA;cAAMyI,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,EACL,CAACjI,WAAW,iBACXb,OAAA;YAAKyI,SAAS,EAAC,+DAA+D;YAAAC,QAAA,eAC5E1I,OAAA;cAAKyI,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B1I,OAAA,CAACN,oBAAoB;gBAAC+I,SAAS,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzE9I,OAAA;gBAAGyI,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9I,OAAA;MAAKyI,SAAS,EAAC,oDAAoD;MAAAC,QAAA,eACjE1I,OAAA;QAAKyI,SAAS,EAAC,6DAA6D;QAAAC,QAAA,gBAE1E1I,OAAA;UACEkJ,OAAO,EAAE5B,WAAY;UACrBmB,SAAS,EAAE,sCACT1H,YAAY,GACR,6BAA6B,GAC7B,+BAA+B,EAClC;UAAA2H,QAAA,EAEF3H,YAAY,gBACXf,OAAA,CAACP,mBAAmB;YAACgJ,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAE3C9I,OAAA,CAACX,cAAc;YAACoJ,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACtC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAGT9I,OAAA;UACEkJ,OAAO,EAAExB,WAAY;UACrBe,SAAS,EAAE,sCACTxH,YAAY,GACR,6BAA6B,GAC7B,+BAA+B,EAClC;UAAAyH,QAAA,EAEFzH,YAAY,gBACXjB,OAAA,CAACN,oBAAoB;YAAC+I,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAE5C9I,OAAA,CAACV,eAAe;YAACmJ,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACvC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAGT9I,OAAA;UACEkJ,OAAO,EAAErB,aAAc;UACvBY,SAAS,EAAE,sCACT,CAACtH,WAAW,GACR,6BAA6B,GAC7B,+BAA+B,EAClC;UAAAuH,QAAA,EAEFvH,WAAW,gBACVnB,OAAA,CAACR,eAAe;YAACiJ,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEvC9I,OAAA,CAACL,oBAAoB;YAAC8I,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAC5C;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAGT9I,OAAA;UACEkJ,OAAO,EAAEnB,OAAQ;UACjBU,SAAS,EAAC,gEAAgE;UAAAC,QAAA,eAE1E1I,OAAA,CAACT,cAAc;YAACkJ,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5I,EAAA,CAvoBID,WAAW;EAAA,QACWd,SAAS,EAClBC,WAAW,EACIU,OAAO;AAAA;AAAAqJ,EAAA,GAHnClJ,WAAW;AAyoBjB,eAAeA,WAAW;AAAC,IAAAkJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}