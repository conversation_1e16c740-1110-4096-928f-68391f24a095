{"ast": null, "code": "var _jsxFileName = \"C:\\\\Projeler\\\\kidgarden\\\\burky_root_web\\\\client\\\\src\\\\pages\\\\expert\\\\messages\\\\MessagesPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../../../hooks/useAuth';\nimport api from '../../../services/api';\nimport { toast } from 'react-hot-toast';\nimport io from 'socket.io-client';\nimport { ChatBubbleLeftEllipsisIcon, MagnifyingGlassIcon, UserIcon, PaperAirplaneIcon, PaperClipIcon, FaceSmileIcon, UserCircleIcon, EllipsisHorizontalIcon, PhoneIcon, VideoCameraIcon, InformationCircleIcon, ClockIcon, CheckCircleIcon, XMarkIcon, TrashIcon, ArchiveBoxIcon, StarIcon } from '@heroicons/react/24/outline';\nimport { format } from 'date-fns';\nimport { tr } from 'date-fns/locale';\nimport { Link } from 'react-router-dom';\n\n/**\n * <PERSON><PERSON> mesaj<PERSON><PERSON> sayfası\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MessagesPage = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [isLoading, setIsLoading] = useState(true);\n  const [conversations, setConversations] = useState([]);\n  const [selectedConversation, setSelectedConversation] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [messageText, setMessageText] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filter, setFilter] = useState('all'); // all, unread, archived, starred\n  const [socket, setSocket] = useState(null);\n  const [onlineUsers, setOnlineUsers] = useState(new Set());\n  const [typingUsers, setTypingUsers] = useState(new Set());\n  const messagesEndRef = useRef(null);\n  const conversationsRef = useRef(null);\n  const messagesContainerRef = useRef(null);\n\n  // Socket.IO connection - ayrı useEffect\n  useEffect(() => {\n    let isMounted = true;\n    const token = localStorage.getItem('accessToken');\n    if (token && user !== null && user !== void 0 && user.id && !(socket !== null && socket !== void 0 && socket.connected)) {\n      console.log('🔌 EXPERT: Socket.IO bağlantısı kuruluyor... User ID:', user.id);\n      const socketConnection = io(process.env.REACT_APP_API_URL || 'https://192.168.1.111:5000', {\n        auth: {\n          token\n        },\n        transports: ['websocket', 'polling'],\n        reconnection: true,\n        reconnectionDelay: 2000,\n        reconnectionAttempts: 3,\n        timeout: 10000,\n        forceNew: false,\n        rejectUnauthorized: false,\n        // Self-signed sertifika için\n        secure: true,\n        upgrade: true,\n        rememberUpgrade: false\n      });\n      setSocket(socketConnection);\n\n      // Connection events\n      socketConnection.on('connect', () => {\n        console.log('✅ EXPERT: Socket.IO bağlantısı kuruldu:', socketConnection.id);\n        console.log('🔗 EXPERT: Socket connected:', socketConnection.connected);\n        console.log('🏠 EXPERT: Socket rooms:', socketConnection.rooms);\n        // Kullanıcının online olduğunu bildir\n        socketConnection.emit('user_online');\n        // Online kullanıcı listesini al\n        socketConnection.emit('get_online_users');\n      });\n      socketConnection.on('disconnect', reason => {\n        console.log('❌ EXPERT: Socket.IO bağlantısı kesildi:', reason);\n      });\n      socketConnection.on('connect_error', error => {\n        console.error('🔥 EXPERT: Socket.IO bağlantı hatası:', error);\n      });\n      socketConnection.on('reconnect', attemptNumber => {\n        console.log('🔄 EXPERT: Socket.IO yeniden bağlandı, deneme:', attemptNumber);\n      });\n      socketConnection.on('reconnect_error', error => {\n        console.error('🔥 EXPERT: Socket.IO yeniden bağlanma hatası:', error);\n      });\n\n      // Heartbeat mekanizması\n      const heartbeatInterval = setInterval(() => {\n        if (socketConnection.connected) {\n          socketConnection.emit('ping', response => {\n            if (response === 'pong') {\n              console.log('💓 EXPERT: Heartbeat OK');\n            }\n          });\n        }\n      }, 30000); // 30 saniyede bir ping gönder\n\n      return () => {\n        isMounted = false;\n        clearInterval(heartbeatInterval);\n\n        // Güvenli disconnect\n        if (socketConnection !== null && socketConnection !== void 0 && socketConnection.connected) {\n          socketConnection.off(); // Tüm event listener'ları kaldır\n          socketConnection.disconnect();\n        }\n      };\n    } else {\n      console.log('❌ EXPERT: Socket bağlantısı kurulamıyor - token veya user eksik:', {\n        hasToken: !!token,\n        hasUser: !!(user !== null && user !== void 0 && user.id),\n        socketConnected: !!(socket !== null && socket !== void 0 && socket.connected)\n      });\n    }\n    return () => {\n      isMounted = false;\n    };\n  }, [user === null || user === void 0 ? void 0 : user.id]);\n\n  // Socket event listeners - ayrı useEffect\n  useEffect(() => {\n    if (socket && user !== null && user !== void 0 && user.id) {\n      console.log('🎧 Socket event listeners kuruluyor...');\n\n      // Sayfa yüklendiğinde online users listesini al\n      if (socket.connected) {\n        console.log('🔄 EXPERT: Requesting online users list on page load');\n        socket.emit('get_online_users');\n      }\n\n      // Socket event listeners\n      const handleNewMessage = message => {\n        console.log('📨 EXPERT: Yeni mesaj alındı:', message);\n        console.log('👤 EXPERT: Current user ID:', user.id);\n        console.log('💬 EXPERT: Message sender ID:', message.senderId);\n        console.log('🏠 EXPERT: Message conversation ID:', message.conversationId);\n\n        // Conversation listesini ÖNCE güncelle (her zaman)\n        setConversations(prev => {\n          console.log('📋 EXPERT: Updating conversations list');\n          return prev.map(conv => conv.id === message.conversationId ? {\n            ...conv,\n            lastMessage: message.content,\n            timestamp: message.createdAt,\n            unread: message.senderId !== user.id\n          } : conv);\n        });\n\n        // Eğer şu anda bu conversation açıksa, mesajı da ekle\n        setSelectedConversation(currentSelected => {\n          console.log('🎯 EXPERT: Current selected conversation:', currentSelected === null || currentSelected === void 0 ? void 0 : currentSelected.id);\n          if (currentSelected && message.conversationId === currentSelected.id) {\n            console.log('✅ EXPERT: Adding message to current conversation');\n            setMessages(prev => {\n              // Mesajın zaten var olup olmadığını kontrol et\n              const messageExists = prev.some(msg => msg.id === message.id);\n              if (messageExists) {\n                console.log('⚠️ EXPERT: Message already exists, skipping:', message.id);\n                return prev;\n              }\n              return [...prev, {\n                id: message.id,\n                senderId: message.senderId,\n                senderName: message.sender.name,\n                senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n                text: message.content,\n                timestamp: message.createdAt,\n                read: message.isRead,\n                delivered: true // Socket'ten gelen mesajlar delivered olarak kabul edilir\n              }];\n            });\n\n            // Eğer mesaj başka birinden geliyorsa ve conversation açıksa, otomatik okundu işaretle\n            if (message.senderId !== user.id) {\n              console.log('📖 EXPERT: Auto-marking message as read since conversation is open');\n              setTimeout(() => {\n                markConversationAsRead(message.conversationId);\n              }, 500); // Kısa bir gecikme ile\n            }\n\n            // Scroll to bottom\n            setTimeout(() => {\n              var _messagesEndRef$curre;\n              (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n                behavior: 'smooth'\n              });\n            }, 100);\n          } else {\n            console.log('❌ EXPERT: Not adding message - conversation not selected or different conversation');\n          }\n          return currentSelected;\n        });\n      };\n      const handleMessageSent = message => {\n        console.log('✅ Mesaj gönderildi onayı:', message);\n      };\n      const handleUserStatusChange = data => {\n        console.log('🔄 EXPERT: User status change:', data);\n        setOnlineUsers(prev => {\n          const newSet = new Set(prev);\n          if (data.status === 'online') {\n            newSet.add(data.userId);\n          } else {\n            newSet.delete(data.userId);\n          }\n          console.log('🟢 EXPERT: Online users updated:', Array.from(newSet));\n          return newSet;\n        });\n      };\n      const handleOnlineUsersList = userIds => {\n        console.log('📋 EXPERT: Online users list received:', userIds);\n        setOnlineUsers(new Set(userIds));\n      };\n      const handleUserTyping = data => {\n        setSelectedConversation(currentSelected => {\n          if (currentSelected && data.conversationId === currentSelected.id) {\n            setTypingUsers(prev => new Set([...prev, data.userId]));\n          }\n          return currentSelected;\n        });\n      };\n      const handleUserStoppedTyping = data => {\n        setTypingUsers(prev => {\n          const newSet = new Set(prev);\n          newSet.delete(data.userId);\n          return newSet;\n        });\n      };\n      const handleMessagesRead = data => {\n        console.log('👁️ EXPERT: Messages read event:', data);\n        console.log('👁️ EXPERT: Read by user:', data.readBy, 'Current user:', user.id);\n        console.log('👁️ EXPERT: Message IDs marked as read:', data.messageIds);\n\n        // Eğer başka bir kullanıcı mesajları okudu ise, kendi gönderdiğim mesajları okundu olarak işaretle\n        if (data.readBy !== user.id) {\n          setMessages(prev => prev.map(msg => {\n            // Kendi gönderdiğim mesajları ve conversation'daki tüm mesajları güncelle\n            if (msg.senderId === user.id && msg.conversationId === data.conversationId) {\n              console.log('👁️ EXPERT: Marking my message as read:', msg.id);\n              return {\n                ...msg,\n                read: true\n              };\n            }\n            // Ayrıca messageIds listesindeki mesajları da güncelle\n            if (data.messageIds.includes(msg.id)) {\n              console.log('👁️ EXPERT: Marking specific message as read:', msg.id);\n              return {\n                ...msg,\n                read: true\n              };\n            }\n            return msg;\n          }));\n\n          // Conversation listesindeki unread durumunu da güncelle\n          setConversations(prev => prev.map(conv => conv.id === data.conversationId ? {\n            ...conv,\n            unread: false\n          } : conv));\n        }\n      };\n\n      // Event listener'ları ekle\n      socket.on('new_message', handleNewMessage);\n      socket.on('message_sent', handleMessageSent);\n      socket.on('user_status_change', handleUserStatusChange);\n      socket.on('user_typing', handleUserTyping);\n      socket.on('user_stopped_typing', handleUserStoppedTyping);\n      socket.on('messages_read', handleMessagesRead);\n      socket.on('online_users_list', handleOnlineUsersList);\n\n      // Cleanup function\n      return () => {\n        socket.off('new_message', handleNewMessage);\n        socket.off('message_sent', handleMessageSent);\n        socket.off('user_status_change', handleUserStatusChange);\n        socket.off('user_typing', handleUserTyping);\n        socket.off('user_stopped_typing', handleUserStoppedTyping);\n        socket.off('messages_read', handleMessagesRead);\n        socket.off('online_users_list', handleOnlineUsersList);\n      };\n    }\n  }, [socket, user.id]);\n\n  // Conversations yükleme - ayrı useEffect\n  useEffect(() => {\n    loadConversations();\n  }, []);\n\n  // Socket bağlandığında tüm conversation'lara join ol - sadece bir kez\n  const [joinedConversations, setJoinedConversations] = useState(new Set());\n  useEffect(() => {\n    if (socket && conversations.length > 0) {\n      console.log('🏠 EXPERT: Tüm conversation\\'lara katılıyor...', conversations.length, 'conversation');\n      conversations.forEach(conversation => {\n        if (!joinedConversations.has(conversation.id)) {\n          socket.emit('join_conversation', conversation.id);\n          console.log(`🏠 EXPERT: Conversation ${conversation.id} katıldı`);\n          setJoinedConversations(prev => new Set([...prev, conversation.id]));\n        }\n      });\n    }\n  }, [socket, conversations, joinedConversations]);\n\n  // Conversations yükleme\n  const loadConversations = async () => {\n    try {\n      setIsLoading(true);\n      const response = await api.get('/messages/conversations');\n\n      // API verisini frontend formatına çevir\n      const formattedConversations = response.data.conversations.map(conversation => {\n        var _conversation$lastMes, _conversation$lastMes2;\n        return {\n          id: conversation.id,\n          clientId: conversation.otherUser.id,\n          clientName: conversation.otherUser.name,\n          lastMessage: ((_conversation$lastMes = conversation.lastMessage) === null || _conversation$lastMes === void 0 ? void 0 : _conversation$lastMes.content) || 'Henüz mesaj yok',\n          timestamp: ((_conversation$lastMes2 = conversation.lastMessage) === null || _conversation$lastMes2 === void 0 ? void 0 : _conversation$lastMes2.timestamp) || conversation.createdAt,\n          unread: conversation.lastMessage ? !conversation.lastMessage.isRead && conversation.lastMessage.senderId !== user.id : false,\n          avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(conversation.otherUser.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n          status: onlineUsers.has(conversation.otherUser.id) ? 'online' : 'offline',\n          starred: false,\n          archived: false\n        };\n      });\n      setConversations(formattedConversations);\n    } catch (error) {\n      console.error('Konuşmalar yüklenirken hata:', error);\n      toast.error('Konuşmalar yüklenemedi');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Mesaj yükleme ve okundu işaretleme\n  useEffect(() => {\n    if (selectedConversation) {\n      loadMessages(selectedConversation.id);\n      markConversationAsRead(selectedConversation.id);\n\n      // Conversation'ı seçildiğinde unread durumunu false yap\n      setConversations(prev => prev.map(conv => conv.id === selectedConversation.id ? {\n        ...conv,\n        unread: false\n      } : conv));\n    }\n  }, [selectedConversation]);\n\n  // Conversation'daki mesajları okundu olarak işaretle\n  const markConversationAsRead = async conversationId => {\n    try {\n      // Server'a okundu bilgisi gönder (mesajlar yüklenmeden önce de çalışır)\n      const response = await api.put(`/messages/conversations/${conversationId}/read`);\n      if (response.data.updatedCount > 0) {\n        console.log('📖 EXPERT: Marked', response.data.updatedCount, 'messages as read');\n\n        // Local state'i güncelle\n        setMessages(prev => prev.map(msg => msg.senderId !== user.id ? {\n          ...msg,\n          read: true\n        } : msg));\n\n        // Socket'e okundu bilgisi gönder\n        if (socket) {\n          socket.emit('mark_messages_read', {\n            conversationId,\n            messageIds: [] // Server zaten hangi mesajların okundu olduğunu biliyor\n          });\n        }\n      }\n    } catch (error) {\n      console.error('Mesajları okundu olarak işaretlerken hata:', error);\n    }\n  };\n\n  // Messages yükleme\n  const loadMessages = async conversationId => {\n    try {\n      const response = await api.get(`/messages/conversations/${conversationId}`);\n\n      // API verisini frontend formatına çevir\n      const formattedMessages = response.data.messages.map(message => ({\n        id: message.id,\n        senderId: message.senderId,\n        senderName: message.sender.name,\n        senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        text: message.content,\n        timestamp: message.createdAt,\n        read: message.isRead,\n        delivered: true,\n        // Veritabanından gelen mesajlar delivered olarak kabul edilir\n        attachments: message.attachments\n      }));\n      setMessages(formattedMessages);\n\n      // Scroll to bottom\n      setTimeout(() => {\n        var _messagesEndRef$curre2;\n        (_messagesEndRef$curre2 = messagesEndRef.current) === null || _messagesEndRef$curre2 === void 0 ? void 0 : _messagesEndRef$curre2.scrollIntoView({\n          behavior: 'smooth'\n        });\n      }, 100);\n    } catch (error) {\n      console.error('Mesajlar yüklenirken hata:', error);\n      toast.error('Mesajlar yüklenemedi');\n    }\n  };\n  // Mesaj gönderme\n  const sendMessage = async () => {\n    if (!messageText.trim() || !selectedConversation) return;\n    console.log('📤 Mesaj gönderiliyor:', {\n      receiverId: selectedConversation.clientId,\n      content: messageText.trim()\n    });\n    try {\n      const response = await api.post('/messages/send', {\n        receiverId: selectedConversation.clientId,\n        content: messageText.trim()\n      });\n      console.log('✅ Mesaj gönderildi:', response.data);\n\n      // Sadece mesaj input'unu temizle - mesaj socket'ten gelecek\n      setMessageText('');\n    } catch (error) {\n      console.error('Mesaj gönderilirken hata:', error);\n      toast.error('Mesaj gönderilemedi');\n    }\n  };\n  useEffect(() => {\n    // Mesajları otomatik kaydır\n    if (messagesEndRef.current && messagesContainerRef.current) {\n      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;\n    }\n  }, [messages]);\n\n  // Mesaj gönderme\n  const handleSendMessage = e => {\n    e.preventDefault();\n    sendMessage();\n  };\n\n  // Konuşma seçme\n  const handleSelectConversation = conversation => {\n    setSelectedConversation(conversation);\n  };\n\n  // Tarih formatı\n  const formatMessageDate = dateString => {\n    const date = new Date(dateString);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    if (date.toDateString() === today.toDateString()) {\n      return format(date, 'HH:mm', {\n        locale: tr\n      });\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Dün ' + format(date, 'HH:mm', {\n        locale: tr\n      });\n    } else {\n      return format(date, 'dd MMM HH:mm', {\n        locale: tr\n      });\n    }\n  };\n\n  // Konuşmaları filtrele\n  const filteredConversations = conversations.filter(conv => {\n    // Arama filtresi\n    const matchesSearch = conv.clientName.toLowerCase().includes(searchTerm.toLowerCase()) || conv.lastMessage.toLowerCase().includes(searchTerm.toLowerCase());\n\n    // Durum filtresi\n    const matchesFilter = filter === 'all' || filter === 'unread' && conv.unread || filter === 'archived' && conv.archived || filter === 'starred' && conv.starred;\n    return matchesSearch && matchesFilter;\n  });\n\n  // Konuşmayı yıldızla\n  const toggleStar = id => {\n    setConversations(prevConversations => prevConversations.map(conv => conv.id === id ? {\n      ...conv,\n      starred: !conv.starred\n    } : conv));\n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({\n        ...prev,\n        starred: !prev.starred\n      }));\n    }\n  };\n\n  // Konuşmayı arşivle\n  const toggleArchive = id => {\n    setConversations(prevConversations => prevConversations.map(conv => conv.id === id ? {\n      ...conv,\n      archived: !conv.archived\n    } : conv));\n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({\n        ...prev,\n        archived: !prev.archived\n      }));\n    }\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 522,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 521,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto px-4 py-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-teal-500 to-teal-700 shadow-lg rounded-lg p-6 mb-6 text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold\",\n            children: \"Mesajlar\\u0131m\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-1 text-indigo-100\",\n            children: \"Dan\\u0131\\u015Fanlar\\u0131n\\u0131zla olan t\\xFCm yaz\\u0131\\u015Fmalar\\u0131n\\u0131z\\u0131 buradan y\\xF6netebilirsiniz.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3 sm:mt-0 flex space-x-2\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-indigo-800 bg-white hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-300\",\n            children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftEllipsisIcon, {\n              className: \"-ml-1 mr-2 h-5 w-5\",\n              \"aria-hidden\": \"true\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 15\n            }, this), \"Yeni Mesaj\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 531,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 530,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-12 h-[75vh]\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-span-12 md:col-span-4 border-r border-gray-200 flex flex-col\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 border-b border-gray-200 bg-white\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-semibold text-gray-800 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftEllipsisIcon, {\n                className: \"h-6 w-6 text-indigo-600 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 17\n              }, this), \"Mesajlar\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3 relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Konu\\u015Fmalarda ara...\",\n                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                className: \"h-5 w-5 text-gray-400 absolute left-3 top-2.5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 565,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3 flex space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: `px-3 py-1 text-sm rounded-full ${filter === 'all' ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                onClick: () => setFilter('all'),\n                children: \"T\\xFCm\\xFC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `px-3 py-1 text-sm rounded-full ${filter === 'unread' ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                onClick: () => setFilter('unread'),\n                children: \"Okunmam\\u0131\\u015F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `px-3 py-1 text-sm rounded-full ${filter === 'starred' ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                onClick: () => setFilter('starred'),\n                children: \"Y\\u0131ld\\u0131zl\\u0131\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `px-3 py-1 text-sm rounded-full ${filter === 'archived' ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`,\n                onClick: () => setFilter('archived'),\n                children: \"Ar\\u015Fiv\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: conversationsRef,\n            style: {\n              height: 'calc(75vh - 145px)',\n              overflowY: 'auto',\n              scrollbarWidth: 'thin',\n              scrollbarColor: '#D1D5DB #F3F4F6'\n            },\n            children: filteredConversations.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 text-center text-gray-500\",\n              children: \"Hi\\xE7 mesaj\\u0131n\\u0131z yok\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 17\n            }, this) : filteredConversations.map(conversation => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `p-3 cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${(selectedConversation === null || selectedConversation === void 0 ? void 0 : selectedConversation.id) === conversation.id ? 'bg-indigo-50' : ''} ${conversation.unread ? 'bg-indigo-50 hover:bg-indigo-100' : ''}`,\n              onClick: () => handleSelectConversation(conversation),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative flex-shrink-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: conversation.avatar,\n                    alt: conversation.clientName,\n                    className: `h-10 w-10 rounded-full ${(selectedConversation === null || selectedConversation === void 0 ? void 0 : selectedConversation.id) === conversation.id ? 'ring-2 ring-indigo-600' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 626,\n                    columnNumber: 25\n                  }, this), conversation.status === 'online' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 636,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 625,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: `text-sm font-medium ${conversation.unread ? 'text-gray-900' : 'text-gray-700'}`,\n                      children: conversation.clientName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 641,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-1\",\n                      children: [conversation.starred && /*#__PURE__*/_jsxDEV(StarIcon, {\n                        className: \"h-4 w-4 text-yellow-400 fill-current\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 646,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: formatMessageDate(conversation.timestamp)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 648,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 644,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 640,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: `text-sm truncate mt-1 ${conversation.unread ? 'text-gray-900 font-medium' : 'text-gray-500'}`,\n                    children: conversation.lastMessage\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 653,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center mt-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-gray-500\",\n                      children: onlineUsers.has(conversation.clientId) ? /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-green-500 font-medium\",\n                        children: \"\\xC7evrimi\\xE7i\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 661,\n                        columnNumber: 33\n                      }, this) : conversation.lastSeen ? `Son görülme: ${conversation.lastSeen}` : ''\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 659,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex space-x-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: e => {\n                          e.stopPropagation();\n                          toggleStar(conversation.id);\n                        },\n                        className: \"text-gray-400 hover:text-yellow-400\",\n                        children: /*#__PURE__*/_jsxDEV(StarIcon, {\n                          className: `h-4 w-4 ${conversation.starred ? 'text-yellow-400 fill-current' : ''}`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 674,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 667,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: e => {\n                          e.stopPropagation();\n                          toggleArchive(conversation.id);\n                        },\n                        className: \"text-gray-400 hover:text-gray-600\",\n                        children: /*#__PURE__*/_jsxDEV(ArchiveBoxIcon, {\n                          className: \"h-4 w-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 683,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 676,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 666,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 658,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 639,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 21\n              }, this), conversation.unread && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"inline-flex items-center ml-12 mt-1 px-2 py-0.5 rounded-full text-xs font-medium bg-indigo-600 text-white\",\n                children: \"Yeni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 690,\n                columnNumber: 23\n              }, this)]\n            }, conversation.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-span-12 md:col-span-8 flex flex-col\",\n          children: selectedConversation ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 border-b border-gray-200 bg-white flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative mr-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: selectedConversation.avatar,\n                    alt: selectedConversation.clientName,\n                    className: \"h-10 w-10 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 708,\n                    columnNumber: 23\n                  }, this), selectedConversation.status === 'online' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 714,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 707,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-lg font-medium text-gray-800\",\n                    children: selectedConversation.clientName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 718,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500\",\n                    children: selectedConversation.status === 'online' ? 'Çevrimiçi' : selectedConversation.lastSeen ? `Son görülme: ${selectedConversation.lastSeen}` : ''\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 721,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 717,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 706,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                  children: /*#__PURE__*/_jsxDEV(PhoneIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 732,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 731,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                  children: /*#__PURE__*/_jsxDEV(VideoCameraIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 735,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 734,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                  children: /*#__PURE__*/_jsxDEV(InformationCircleIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 738,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 737,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"p-2 rounded-full hover:bg-gray-100 text-gray-600\",\n                  children: /*#__PURE__*/_jsxDEV(EllipsisHorizontalIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 741,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 740,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 730,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 705,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: messagesContainerRef,\n              className: \"p-4 bg-gray-50\",\n              style: {\n                height: 'calc(75vh - 195px)',\n                overflowY: 'auto',\n                scrollbarWidth: 'thin',\n                scrollbarColor: '#D1D5DB #F3F4F6'\n              },\n              children: [messages.map((message, index) => {\n                const isSender = message.senderId === user.id;\n                const showAvatar = index === 0 || messages[index - 1].senderId !== message.senderId;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `flex ${isSender ? 'justify-end' : 'justify-start'} mb-4`,\n                  children: [!isSender && showAvatar && /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: message.senderAvatar,\n                    alt: message.senderName,\n                    className: \"h-8 w-8 rounded-full mr-2 mt-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 764,\n                    columnNumber: 27\n                  }, this), !isSender && !showAvatar && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 770,\n                    columnNumber: 54\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `max-w-xs md:max-w-md lg:max-w-lg rounded-lg px-4 py-2 ${isSender ? 'bg-indigo-600 text-white rounded-br-none' : 'bg-white text-gray-800 rounded-bl-none border border-gray-200'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm\",\n                      children: message.text\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 778,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `text-xs mt-1 ${isSender ? 'text-indigo-200' : 'text-gray-500'} flex items-center justify-end`,\n                      children: [formatMessageDate(message.timestamp), isSender && /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                        className: `h-3 w-3 ml-1 ${message.read ? 'text-blue-400' : 'text-gray-400'}`,\n                        title: message.read ? 'Okundu' : 'İletildi'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 782,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 779,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 771,\n                    columnNumber: 25\n                  }, this), isSender && showAvatar && /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: message.senderAvatar,\n                    alt: message.senderName,\n                    className: \"h-8 w-8 rounded-full ml-2 mt-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 790,\n                    columnNumber: 27\n                  }, this), isSender && !showAvatar && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 ml-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 796,\n                    columnNumber: 53\n                  }, this)]\n                }, message.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 762,\n                  columnNumber: 23\n                }, this);\n              }), /*#__PURE__*/_jsxDEV(\"div\", {\n                ref: messagesEndRef\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 800,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 747,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 border-t border-gray-200 bg-white\",\n              children: /*#__PURE__*/_jsxDEV(\"form\", {\n                onSubmit: handleSendMessage,\n                className: \"flex items-end\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\",\n                  children: /*#__PURE__*/_jsxDEV(PaperClipIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 810,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 806,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    className: \"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none\",\n                    placeholder: \"Mesaj\\u0131n\\u0131z\\u0131 yaz\\u0131n...\",\n                    rows: \"2\",\n                    value: messageText,\n                    onChange: e => setMessageText(e.target.value),\n                    onKeyDown: e => {\n                      if (e.key === 'Enter' && !e.shiftKey) {\n                        e.preventDefault();\n                        sendMessage();\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 813,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 812,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\",\n                  children: /*#__PURE__*/_jsxDEV(FaceSmileIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 831,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 827,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"submit\",\n                  disabled: !messageText.trim(),\n                  className: `ml-2 p-2 rounded-full ${messageText.trim() ? 'bg-indigo-600 text-white hover:bg-indigo-700' : 'bg-gray-200 text-gray-400 cursor-not-allowed'} focus:outline-none`,\n                  children: /*#__PURE__*/_jsxDEV(PaperAirplaneIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 842,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 833,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 805,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 804,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) :\n          /*#__PURE__*/\n          // Mesaj seçilmediğinde\n          _jsxDEV(\"div\", {\n            className: \"flex-1 flex flex-col items-center justify-center p-6 bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full max-w-md text-center\",\n              children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftEllipsisIcon, {\n                className: \"h-16 w-16 text-gray-300 mb-4 mx-auto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 851,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-medium text-gray-800 mb-2\",\n                children: \"Mesajlar\\u0131n\\u0131z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 852,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-500 mx-auto\",\n                children: \"Mesajla\\u015Fmaya ba\\u015Flamak i\\xE7in sol taraftan bir konu\\u015Fma se\\xE7in veya yeni bir mesaj ba\\u015Flat\\u0131n.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 853,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 850,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 849,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 701,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 549,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 548,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 528,\n    columnNumber: 5\n  }, this);\n};\n_s(MessagesPage, \"8B7RxxYStSTw62EYx5HVIfMSfbM=\", false, function () {\n  return [useAuth];\n});\n_c = MessagesPage;\nexport default MessagesPage;\nvar _c;\n$RefreshReg$(_c, \"MessagesPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useAuth", "api", "toast", "io", "ChatBubbleLeftEllipsisIcon", "MagnifyingGlassIcon", "UserIcon", "PaperAirplaneIcon", "PaperClipIcon", "FaceSmileIcon", "UserCircleIcon", "EllipsisHorizontalIcon", "PhoneIcon", "VideoCameraIcon", "InformationCircleIcon", "ClockIcon", "CheckCircleIcon", "XMarkIcon", "TrashIcon", "ArchiveBoxIcon", "StarIcon", "format", "tr", "Link", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MessagesPage", "_s", "user", "isLoading", "setIsLoading", "conversations", "setConversations", "selectedConversation", "setSelectedConversation", "messages", "setMessages", "messageText", "setMessageText", "searchTerm", "setSearchTerm", "filter", "setFilter", "socket", "setSocket", "onlineUsers", "setOnlineUsers", "Set", "typingUsers", "setTypingUsers", "messagesEndRef", "conversationsRef", "messagesContainerRef", "isMounted", "token", "localStorage", "getItem", "id", "connected", "console", "log", "socketConnection", "process", "env", "REACT_APP_API_URL", "auth", "transports", "reconnection", "reconnectionDelay", "reconnectionAttempts", "timeout", "forceNew", "rejectUnauthorized", "secure", "upgrade", "rememberUpgrade", "on", "rooms", "emit", "reason", "error", "attemptNumber", "heartbeatInterval", "setInterval", "response", "clearInterval", "off", "disconnect", "hasToken", "<PERSON><PERSON>ser", "socketConnected", "handleNewMessage", "message", "senderId", "conversationId", "prev", "map", "conv", "lastMessage", "content", "timestamp", "createdAt", "unread", "currentSelected", "messageExists", "some", "msg", "sender<PERSON>ame", "sender", "name", "senderAvatar", "encodeURIComponent", "text", "read", "isRead", "delivered", "setTimeout", "markConversationAsRead", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleMessageSent", "handleUserStatusChange", "data", "newSet", "status", "add", "userId", "delete", "Array", "from", "handleOnlineUsersList", "userIds", "handleUserTyping", "handleUserStoppedTyping", "handleMessagesRead", "readBy", "messageIds", "includes", "loadConversations", "joinedConversations", "setJoinedConversations", "length", "for<PERSON>ach", "conversation", "has", "get", "formattedConversations", "_conversation$lastMes", "_conversation$lastMes2", "clientId", "otherUser", "clientName", "avatar", "starred", "archived", "loadMessages", "put", "updatedCount", "formattedMessages", "attachments", "_messagesEndRef$curre2", "sendMessage", "trim", "receiverId", "post", "scrollTop", "scrollHeight", "handleSendMessage", "e", "preventDefault", "handleSelectConversation", "formatMessageDate", "dateString", "date", "Date", "today", "yesterday", "setDate", "getDate", "toDateString", "locale", "filteredConversations", "matchesSearch", "toLowerCase", "matchesFilter", "toggleStar", "prevConversations", "toggleArchive", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "value", "onChange", "target", "onClick", "ref", "style", "height", "overflowY", "scrollbarWidth", "scrollbarColor", "src", "alt", "lastSeen", "stopPropagation", "index", "isSender", "showAvatar", "title", "onSubmit", "rows", "onKeyDown", "key", "shift<PERSON>ey", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Projeler/kidgarden/burky_root_web/client/src/pages/expert/messages/MessagesPage.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../../../hooks/useAuth';\nimport api from '../../../services/api';\nimport { toast } from 'react-hot-toast';\nimport io from 'socket.io-client';\nimport {\n  ChatBubbleLeftEllipsisIcon,\n  MagnifyingGlassIcon,\n  UserIcon,\n  PaperAirplaneIcon,\n  PaperClipIcon,\n  FaceSmileIcon,\n  UserCircleIcon,\n  EllipsisHorizontalIcon,\n  PhoneIcon,\n  VideoCameraIcon,\n  InformationCircleIcon,\n  ClockIcon,\n  CheckCircleIcon,\n  XMarkIcon,\n  TrashIcon,\n  ArchiveBoxIcon,\n  StarIcon\n} from '@heroicons/react/24/outline';\nimport { format } from 'date-fns';\nimport { tr } from 'date-fns/locale';\nimport { Link } from 'react-router-dom';\n\n/**\n * <PERSON><PERSON> mesajlaşma sayfası\n */\nconst MessagesPage = () => {\n  const { user } = useAuth();\n  const [isLoading, setIsLoading] = useState(true);\n  const [conversations, setConversations] = useState([]);\n  const [selectedConversation, setSelectedConversation] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [messageText, setMessageText] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filter, setFilter] = useState('all'); // all, unread, archived, starred\n  const [socket, setSocket] = useState(null);\n  const [onlineUsers, setOnlineUsers] = useState(new Set());\n  const [typingUsers, setTypingUsers] = useState(new Set());\n  const messagesEndRef = useRef(null);\n  const conversationsRef = useRef(null);\n  const messagesContainerRef = useRef(null);\n\n  // Socket.IO connection - ayrı useEffect\n  useEffect(() => {\n    let isMounted = true;\n\n    const token = localStorage.getItem('accessToken');\n    if (token && user?.id && !socket?.connected) {\n      console.log('🔌 EXPERT: Socket.IO bağlantısı kuruluyor... User ID:', user.id);\n      const socketConnection = io(process.env.REACT_APP_API_URL || 'https://192.168.1.111:5000', {\n        auth: { token },\n        transports: ['websocket', 'polling'],\n        reconnection: true,\n        reconnectionDelay: 2000,\n        reconnectionAttempts: 3,\n        timeout: 10000,\n        forceNew: false,\n        rejectUnauthorized: false, // Self-signed sertifika için\n        secure: true,\n        upgrade: true,\n        rememberUpgrade: false\n      });\n\n      setSocket(socketConnection);\n\n      // Connection events\n      socketConnection.on('connect', () => {\n        console.log('✅ EXPERT: Socket.IO bağlantısı kuruldu:', socketConnection.id);\n        console.log('🔗 EXPERT: Socket connected:', socketConnection.connected);\n        console.log('🏠 EXPERT: Socket rooms:', socketConnection.rooms);\n        // Kullanıcının online olduğunu bildir\n        socketConnection.emit('user_online');\n        // Online kullanıcı listesini al\n        socketConnection.emit('get_online_users');\n      });\n\n      socketConnection.on('disconnect', (reason) => {\n        console.log('❌ EXPERT: Socket.IO bağlantısı kesildi:', reason);\n      });\n\n      socketConnection.on('connect_error', (error) => {\n        console.error('🔥 EXPERT: Socket.IO bağlantı hatası:', error);\n      });\n\n      socketConnection.on('reconnect', (attemptNumber) => {\n        console.log('🔄 EXPERT: Socket.IO yeniden bağlandı, deneme:', attemptNumber);\n      });\n\n      socketConnection.on('reconnect_error', (error) => {\n        console.error('🔥 EXPERT: Socket.IO yeniden bağlanma hatası:', error);\n      });\n\n      // Heartbeat mekanizması\n      const heartbeatInterval = setInterval(() => {\n        if (socketConnection.connected) {\n          socketConnection.emit('ping', (response) => {\n            if (response === 'pong') {\n              console.log('💓 EXPERT: Heartbeat OK');\n            }\n          });\n        }\n      }, 30000); // 30 saniyede bir ping gönder\n\n      return () => {\n        isMounted = false;\n        clearInterval(heartbeatInterval);\n\n        // Güvenli disconnect\n        if (socketConnection?.connected) {\n          socketConnection.off(); // Tüm event listener'ları kaldır\n          socketConnection.disconnect();\n        }\n      };\n    } else {\n      console.log('❌ EXPERT: Socket bağlantısı kurulamıyor - token veya user eksik:', {\n        hasToken: !!token,\n        hasUser: !!user?.id,\n        socketConnected: !!socket?.connected\n      });\n    }\n\n    return () => {\n      isMounted = false;\n    };\n  }, [user?.id]);\n\n  // Socket event listeners - ayrı useEffect\n  useEffect(() => {\n    if (socket && user?.id) {\n      console.log('🎧 Socket event listeners kuruluyor...');\n\n      // Sayfa yüklendiğinde online users listesini al\n      if (socket.connected) {\n        console.log('🔄 EXPERT: Requesting online users list on page load');\n        socket.emit('get_online_users');\n      }\n\n      // Socket event listeners\n      const handleNewMessage = (message) => {\n        console.log('📨 EXPERT: Yeni mesaj alındı:', message);\n        console.log('👤 EXPERT: Current user ID:', user.id);\n        console.log('💬 EXPERT: Message sender ID:', message.senderId);\n        console.log('🏠 EXPERT: Message conversation ID:', message.conversationId);\n\n        // Conversation listesini ÖNCE güncelle (her zaman)\n        setConversations(prev => {\n          console.log('📋 EXPERT: Updating conversations list');\n          return prev.map(conv =>\n            conv.id === message.conversationId\n              ? { ...conv, lastMessage: message.content, timestamp: message.createdAt, unread: message.senderId !== user.id }\n              : conv\n          );\n        });\n\n        // Eğer şu anda bu conversation açıksa, mesajı da ekle\n        setSelectedConversation(currentSelected => {\n          console.log('🎯 EXPERT: Current selected conversation:', currentSelected?.id);\n          if (currentSelected && message.conversationId === currentSelected.id) {\n            console.log('✅ EXPERT: Adding message to current conversation');\n            setMessages(prev => {\n              // Mesajın zaten var olup olmadığını kontrol et\n              const messageExists = prev.some(msg => msg.id === message.id);\n              if (messageExists) {\n                console.log('⚠️ EXPERT: Message already exists, skipping:', message.id);\n                return prev;\n              }\n\n              return [...prev, {\n                id: message.id,\n                senderId: message.senderId,\n                senderName: message.sender.name,\n                senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n                text: message.content,\n                timestamp: message.createdAt,\n                read: message.isRead,\n                delivered: true // Socket'ten gelen mesajlar delivered olarak kabul edilir\n              }];\n            });\n\n            // Eğer mesaj başka birinden geliyorsa ve conversation açıksa, otomatik okundu işaretle\n            if (message.senderId !== user.id) {\n              console.log('📖 EXPERT: Auto-marking message as read since conversation is open');\n              setTimeout(() => {\n                markConversationAsRead(message.conversationId);\n              }, 500); // Kısa bir gecikme ile\n            }\n\n            // Scroll to bottom\n            setTimeout(() => {\n              messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n            }, 100);\n          } else {\n            console.log('❌ EXPERT: Not adding message - conversation not selected or different conversation');\n          }\n          return currentSelected;\n        });\n      };\n\n      const handleMessageSent = (message) => {\n        console.log('✅ Mesaj gönderildi onayı:', message);\n      };\n\n      const handleUserStatusChange = (data) => {\n        console.log('🔄 EXPERT: User status change:', data);\n        setOnlineUsers(prev => {\n          const newSet = new Set(prev);\n          if (data.status === 'online') {\n            newSet.add(data.userId);\n          } else {\n            newSet.delete(data.userId);\n          }\n          console.log('🟢 EXPERT: Online users updated:', Array.from(newSet));\n          return newSet;\n        });\n      };\n\n      const handleOnlineUsersList = (userIds) => {\n        console.log('📋 EXPERT: Online users list received:', userIds);\n        setOnlineUsers(new Set(userIds));\n      };\n\n      const handleUserTyping = (data) => {\n        setSelectedConversation(currentSelected => {\n          if (currentSelected && data.conversationId === currentSelected.id) {\n            setTypingUsers(prev => new Set([...prev, data.userId]));\n          }\n          return currentSelected;\n        });\n      };\n\n      const handleUserStoppedTyping = (data) => {\n        setTypingUsers(prev => {\n          const newSet = new Set(prev);\n          newSet.delete(data.userId);\n          return newSet;\n        });\n      };\n\n      const handleMessagesRead = (data) => {\n        console.log('👁️ EXPERT: Messages read event:', data);\n        console.log('👁️ EXPERT: Read by user:', data.readBy, 'Current user:', user.id);\n        console.log('👁️ EXPERT: Message IDs marked as read:', data.messageIds);\n\n        // Eğer başka bir kullanıcı mesajları okudu ise, kendi gönderdiğim mesajları okundu olarak işaretle\n        if (data.readBy !== user.id) {\n          setMessages(prev => prev.map(msg => {\n            // Kendi gönderdiğim mesajları ve conversation'daki tüm mesajları güncelle\n            if (msg.senderId === user.id && msg.conversationId === data.conversationId) {\n              console.log('👁️ EXPERT: Marking my message as read:', msg.id);\n              return { ...msg, read: true };\n            }\n            // Ayrıca messageIds listesindeki mesajları da güncelle\n            if (data.messageIds.includes(msg.id)) {\n              console.log('👁️ EXPERT: Marking specific message as read:', msg.id);\n              return { ...msg, read: true };\n            }\n            return msg;\n          }));\n\n          // Conversation listesindeki unread durumunu da güncelle\n          setConversations(prev => prev.map(conv =>\n            conv.id === data.conversationId\n              ? { ...conv, unread: false }\n              : conv\n          ));\n        }\n      };\n\n      // Event listener'ları ekle\n      socket.on('new_message', handleNewMessage);\n      socket.on('message_sent', handleMessageSent);\n      socket.on('user_status_change', handleUserStatusChange);\n      socket.on('user_typing', handleUserTyping);\n      socket.on('user_stopped_typing', handleUserStoppedTyping);\n      socket.on('messages_read', handleMessagesRead);\n      socket.on('online_users_list', handleOnlineUsersList);\n\n      // Cleanup function\n      return () => {\n        socket.off('new_message', handleNewMessage);\n        socket.off('message_sent', handleMessageSent);\n        socket.off('user_status_change', handleUserStatusChange);\n        socket.off('user_typing', handleUserTyping);\n        socket.off('user_stopped_typing', handleUserStoppedTyping);\n        socket.off('messages_read', handleMessagesRead);\n        socket.off('online_users_list', handleOnlineUsersList);\n      };\n    }\n  }, [socket, user.id]);\n\n  // Conversations yükleme - ayrı useEffect\n  useEffect(() => {\n    loadConversations();\n  }, []);\n\n  // Socket bağlandığında tüm conversation'lara join ol - sadece bir kez\n  const [joinedConversations, setJoinedConversations] = useState(new Set());\n\n  useEffect(() => {\n    if (socket && conversations.length > 0) {\n      console.log('🏠 EXPERT: Tüm conversation\\'lara katılıyor...', conversations.length, 'conversation');\n\n      conversations.forEach(conversation => {\n        if (!joinedConversations.has(conversation.id)) {\n          socket.emit('join_conversation', conversation.id);\n          console.log(`🏠 EXPERT: Conversation ${conversation.id} katıldı`);\n          setJoinedConversations(prev => new Set([...prev, conversation.id]));\n        }\n      });\n    }\n  }, [socket, conversations, joinedConversations]);\n\n  // Conversations yükleme\n  const loadConversations = async () => {\n    try {\n      setIsLoading(true);\n      const response = await api.get('/messages/conversations');\n\n      // API verisini frontend formatına çevir\n      const formattedConversations = response.data.conversations.map(conversation => ({\n        id: conversation.id,\n        clientId: conversation.otherUser.id,\n        clientName: conversation.otherUser.name,\n        lastMessage: conversation.lastMessage?.content || 'Henüz mesaj yok',\n        timestamp: conversation.lastMessage?.timestamp || conversation.createdAt,\n        unread: conversation.lastMessage ? !conversation.lastMessage.isRead && conversation.lastMessage.senderId !== user.id : false,\n        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(conversation.otherUser.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        status: onlineUsers.has(conversation.otherUser.id) ? 'online' : 'offline',\n        starred: false,\n        archived: false\n      }));\n\n      setConversations(formattedConversations);\n    } catch (error) {\n      console.error('Konuşmalar yüklenirken hata:', error);\n      toast.error('Konuşmalar yüklenemedi');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Mesaj yükleme ve okundu işaretleme\n  useEffect(() => {\n    if (selectedConversation) {\n      loadMessages(selectedConversation.id);\n      markConversationAsRead(selectedConversation.id);\n\n      // Conversation'ı seçildiğinde unread durumunu false yap\n      setConversations(prev => prev.map(conv =>\n        conv.id === selectedConversation.id\n          ? { ...conv, unread: false }\n          : conv\n      ));\n    }\n  }, [selectedConversation]);\n\n  // Conversation'daki mesajları okundu olarak işaretle\n  const markConversationAsRead = async (conversationId) => {\n    try {\n      // Server'a okundu bilgisi gönder (mesajlar yüklenmeden önce de çalışır)\n      const response = await api.put(`/messages/conversations/${conversationId}/read`);\n\n      if (response.data.updatedCount > 0) {\n        console.log('📖 EXPERT: Marked', response.data.updatedCount, 'messages as read');\n\n        // Local state'i güncelle\n        setMessages(prev => prev.map(msg =>\n          msg.senderId !== user.id ? { ...msg, read: true } : msg\n        ));\n\n        // Socket'e okundu bilgisi gönder\n        if (socket) {\n          socket.emit('mark_messages_read', {\n            conversationId,\n            messageIds: [] // Server zaten hangi mesajların okundu olduğunu biliyor\n          });\n        }\n      }\n    } catch (error) {\n      console.error('Mesajları okundu olarak işaretlerken hata:', error);\n    }\n  };\n\n  // Messages yükleme\n  const loadMessages = async (conversationId) => {\n    try {\n      const response = await api.get(`/messages/conversations/${conversationId}`);\n\n      // API verisini frontend formatına çevir\n      const formattedMessages = response.data.messages.map(message => ({\n        id: message.id,\n        senderId: message.senderId,\n        senderName: message.sender.name,\n        senderAvatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(message.sender.name)}&background=4f46e5&color=fff&size=200&font-size=0.6`,\n        text: message.content,\n        timestamp: message.createdAt,\n        read: message.isRead,\n        delivered: true, // Veritabanından gelen mesajlar delivered olarak kabul edilir\n        attachments: message.attachments\n      }));\n\n      setMessages(formattedMessages);\n\n      // Scroll to bottom\n      setTimeout(() => {\n        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n      }, 100);\n    } catch (error) {\n      console.error('Mesajlar yüklenirken hata:', error);\n      toast.error('Mesajlar yüklenemedi');\n    }\n  };\n  // Mesaj gönderme\n  const sendMessage = async () => {\n    if (!messageText.trim() || !selectedConversation) return;\n\n    console.log('📤 Mesaj gönderiliyor:', {\n      receiverId: selectedConversation.clientId,\n      content: messageText.trim()\n    });\n\n    try {\n      const response = await api.post('/messages/send', {\n        receiverId: selectedConversation.clientId,\n        content: messageText.trim()\n      });\n\n      console.log('✅ Mesaj gönderildi:', response.data);\n\n      // Sadece mesaj input'unu temizle - mesaj socket'ten gelecek\n      setMessageText('');\n\n    } catch (error) {\n      console.error('Mesaj gönderilirken hata:', error);\n      toast.error('Mesaj gönderilemedi');\n    }\n  };\n\n  useEffect(() => {\n    // Mesajları otomatik kaydır\n    if (messagesEndRef.current && messagesContainerRef.current) {\n      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;\n    }\n  }, [messages]);\n\n  // Mesaj gönderme\n  const handleSendMessage = (e) => {\n    e.preventDefault();\n    sendMessage();\n  };\n\n  // Konuşma seçme\n  const handleSelectConversation = (conversation) => {\n    setSelectedConversation(conversation);\n  };\n\n  // Tarih formatı\n  const formatMessageDate = (dateString) => {\n    const date = new Date(dateString);\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    \n    if (date.toDateString() === today.toDateString()) {\n      return format(date, 'HH:mm', { locale: tr });\n    } else if (date.toDateString() === yesterday.toDateString()) {\n      return 'Dün ' + format(date, 'HH:mm', { locale: tr });\n    } else {\n      return format(date, 'dd MMM HH:mm', { locale: tr });\n    }\n  };\n\n  // Konuşmaları filtrele\n  const filteredConversations = conversations.filter(conv => {\n    // Arama filtresi\n    const matchesSearch = conv.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         conv.lastMessage.toLowerCase().includes(searchTerm.toLowerCase());\n    \n    // Durum filtresi\n    const matchesFilter = filter === 'all' ||\n                         (filter === 'unread' && conv.unread) ||\n                         (filter === 'archived' && conv.archived) ||\n                         (filter === 'starred' && conv.starred);\n                        \n    return matchesSearch && matchesFilter;\n  });\n\n  // Konuşmayı yıldızla\n  const toggleStar = (id) => {\n    setConversations(prevConversations =>\n      prevConversations.map(conv =>\n        conv.id === id ? { ...conv, starred: !conv.starred } : conv\n      )\n    );\n    \n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({ ...prev, starred: !prev.starred }));\n    }\n  };\n\n  // Konuşmayı arşivle\n  const toggleArchive = (id) => {\n    setConversations(prevConversations =>\n      prevConversations.map(conv =>\n        conv.id === id ? { ...conv, archived: !conv.archived } : conv\n      )\n    );\n    \n    if (selectedConversation && selectedConversation.id === id) {\n      setSelectedConversation(prev => ({ ...prev, archived: !prev.archived }));\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 py-6\">\n      {/* Sayfa Başlığı */}\n      <div className=\"bg-gradient-to-r from-teal-500 to-teal-700 shadow-lg rounded-lg p-6 mb-6 text-white\">\n        <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center\">\n          <div>\n            <h1 className=\"text-2xl font-bold\">Mesajlarım</h1>\n            <p className=\"mt-1 text-indigo-100\">\n              Danışanlarınızla olan tüm yazışmalarınızı buradan yönetebilirsiniz.\n            </p>\n          </div>\n          <div className=\"mt-3 sm:mt-0 flex space-x-2\">\n            <button className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-indigo-800 bg-white hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-300\">\n              <ChatBubbleLeftEllipsisIcon className=\"-ml-1 mr-2 h-5 w-5\" aria-hidden=\"true\" />\n              Yeni Mesaj\n            </button>\n          </div>\n        </div>\n      </div>\n      \n      {/* Mesajlaşma arayüzü */}\n      <div className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n        <div className=\"grid grid-cols-12 h-[75vh]\">\n          {/* Sol Kenar - Konuşma Listesi */}\n          <div className=\"col-span-12 md:col-span-4 border-r border-gray-200 flex flex-col\">\n            <div className=\"p-4 border-b border-gray-200 bg-white\">\n              <h1 className=\"text-xl font-semibold text-gray-800 flex items-center\">\n                <ChatBubbleLeftEllipsisIcon className=\"h-6 w-6 text-indigo-600 mr-2\" />\n                Mesajlar\n              </h1>\n              <div className=\"mt-3 relative\">\n                <input\n                  type=\"text\"\n                  placeholder=\"Konuşmalarda ara...\"\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                />\n                <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400 absolute left-3 top-2.5\" />\n              </div>\n              <div className=\"mt-3 flex space-x-2\">\n                <button\n                  className={`px-3 py-1 text-sm rounded-full ${filter === 'all' \n                    ? 'bg-indigo-100 text-indigo-800' \n                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                  onClick={() => setFilter('all')}\n                >\n                  Tümü\n                </button>\n                <button\n                  className={`px-3 py-1 text-sm rounded-full ${filter === 'unread' \n                    ? 'bg-indigo-100 text-indigo-800' \n                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                  onClick={() => setFilter('unread')}\n                >\n                  Okunmamış\n                </button>\n                <button\n                  className={`px-3 py-1 text-sm rounded-full ${filter === 'starred' \n                    ? 'bg-indigo-100 text-indigo-800' \n                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                  onClick={() => setFilter('starred')}\n                >\n                  Yıldızlı\n                </button>\n                <button\n                  className={`px-3 py-1 text-sm rounded-full ${filter === 'archived' \n                    ? 'bg-indigo-100 text-indigo-800' \n                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'}`}\n                  onClick={() => setFilter('archived')}\n                >\n                  Arşiv\n                </button>\n              </div>\n            </div>\n            <div \n              ref={conversationsRef}\n              style={{\n                height: 'calc(75vh - 145px)',\n                overflowY: 'auto',\n                scrollbarWidth: 'thin',\n                scrollbarColor: '#D1D5DB #F3F4F6'\n              }}\n            >\n              {filteredConversations.length === 0 ? (\n                <div className=\"p-4 text-center text-gray-500\">\n                  Hiç mesajınız yok\n                </div>\n              ) : (\n                filteredConversations.map(conversation => (\n                  <div\n                    key={conversation.id}\n                    className={`p-3 cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${\n                      selectedConversation?.id === conversation.id ? 'bg-indigo-50' : ''\n                    } ${conversation.unread ? 'bg-indigo-50 hover:bg-indigo-100' : ''}`}\n                    onClick={() => handleSelectConversation(conversation)}\n                  >\n                    <div className=\"flex items-start space-x-3\">\n                      <div className=\"relative flex-shrink-0\">\n                        <img\n                          src={conversation.avatar}\n                          alt={conversation.clientName}\n                          className={`h-10 w-10 rounded-full ${\n                            selectedConversation?.id === conversation.id \n                              ? 'ring-2 ring-indigo-600' \n                              : ''\n                          }`}\n                        />\n                        {conversation.status === 'online' && (\n                          <span className=\"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"></span>\n                        )}\n                      </div>\n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex justify-between items-start\">\n                          <h3 className={`text-sm font-medium ${conversation.unread ? 'text-gray-900' : 'text-gray-700'}`}>\n                            {conversation.clientName}\n                          </h3>\n                          <div className=\"flex items-center space-x-1\">\n                            {conversation.starred && (\n                              <StarIcon className=\"h-4 w-4 text-yellow-400 fill-current\" />\n                            )}\n                            <span className=\"text-xs text-gray-500\">\n                              {formatMessageDate(conversation.timestamp)}\n                            </span>\n                          </div>\n                        </div>\n                        <p className={`text-sm truncate mt-1 ${\n                          conversation.unread ? 'text-gray-900 font-medium' : 'text-gray-500'\n                        }`}>\n                          {conversation.lastMessage}\n                        </p>\n                        <div className=\"flex justify-between items-center mt-1\">\n                          <span className=\"text-xs text-gray-500\">\n                            {onlineUsers.has(conversation.clientId)\n                              ? <span className=\"text-green-500 font-medium\">Çevrimiçi</span>\n                              : conversation.lastSeen\n                                ? `Son görülme: ${conversation.lastSeen}`\n                                : ''}\n                          </span>\n                          <div className=\"flex space-x-1\">\n                            <button \n                              onClick={(e) => {\n                                e.stopPropagation();\n                                toggleStar(conversation.id);\n                              }}\n                              className=\"text-gray-400 hover:text-yellow-400\"\n                            >\n                              <StarIcon className={`h-4 w-4 ${conversation.starred ? 'text-yellow-400 fill-current' : ''}`} />\n                            </button>\n                            <button \n                              onClick={(e) => {\n                                e.stopPropagation();\n                                toggleArchive(conversation.id);\n                              }}\n                              className=\"text-gray-400 hover:text-gray-600\"\n                            >\n                              <ArchiveBoxIcon className=\"h-4 w-4\" />\n                            </button>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {conversation.unread && (\n                      <span className=\"inline-flex items-center ml-12 mt-1 px-2 py-0.5 rounded-full text-xs font-medium bg-indigo-600 text-white\">\n                        Yeni\n                      </span>\n                    )}\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n\n          {/* Sağ Taraf - Mesaj Alanı */}\n          <div className=\"col-span-12 md:col-span-8 flex flex-col\">\n            {selectedConversation ? (\n              <>\n                {/* Mesajlaşma Başlığı */}\n                <div className=\"p-4 border-b border-gray-200 bg-white flex justify-between items-center\">\n                  <div className=\"flex items-center\">\n                    <div className=\"relative mr-3\">\n                      <img\n                        src={selectedConversation.avatar}\n                        alt={selectedConversation.clientName}\n                        className=\"h-10 w-10 rounded-full\"\n                      />\n                      {selectedConversation.status === 'online' && (\n                        <span className=\"absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full bg-green-400 ring-1 ring-white\"></span>\n                      )}\n                    </div>\n                    <div>\n                      <h2 className=\"text-lg font-medium text-gray-800\">\n                        {selectedConversation.clientName}\n                      </h2>\n                      <p className=\"text-xs text-gray-500\">\n                        {selectedConversation.status === 'online' \n                          ? 'Çevrimiçi' \n                          : selectedConversation.lastSeen \n                            ? `Son görülme: ${selectedConversation.lastSeen}` \n                            : ''}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <button className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\">\n                      <PhoneIcon className=\"h-5 w-5\" />\n                    </button>\n                    <button className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\">\n                      <VideoCameraIcon className=\"h-5 w-5\" />\n                    </button>\n                    <button className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\">\n                      <InformationCircleIcon className=\"h-5 w-5\" />\n                    </button>\n                    <button className=\"p-2 rounded-full hover:bg-gray-100 text-gray-600\">\n                      <EllipsisHorizontalIcon className=\"h-5 w-5\" />\n                    </button>\n                  </div>\n                </div>\n\n                {/* Mesaj Alanı */}\n                <div \n                  ref={messagesContainerRef}\n                  className=\"p-4 bg-gray-50\"\n                  style={{\n                    height: 'calc(75vh - 195px)',\n                    overflowY: 'auto',\n                    scrollbarWidth: 'thin',\n                    scrollbarColor: '#D1D5DB #F3F4F6'\n                  }}\n                >\n                  {messages.map((message, index) => {\n                    const isSender = message.senderId === user.id;\n                    const showAvatar = index === 0 || messages[index - 1].senderId !== message.senderId;\n                    \n                    return (\n                      <div key={message.id} className={`flex ${isSender ? 'justify-end' : 'justify-start'} mb-4`}>\n                        {!isSender && showAvatar && (\n                          <img \n                            src={message.senderAvatar} \n                            alt={message.senderName}\n                            className=\"h-8 w-8 rounded-full mr-2 mt-1\"\n                          />\n                        )}\n                        {!isSender && !showAvatar && <div className=\"w-8 mr-2\"></div>}\n                        <div \n                          className={`max-w-xs md:max-w-md lg:max-w-lg rounded-lg px-4 py-2 ${\n                            isSender \n                              ? 'bg-indigo-600 text-white rounded-br-none' \n                              : 'bg-white text-gray-800 rounded-bl-none border border-gray-200'\n                          }`}\n                        >\n                          <p className=\"text-sm\">{message.text}</p>\n                          <div className={`text-xs mt-1 ${isSender ? 'text-indigo-200' : 'text-gray-500'} flex items-center justify-end`}>\n                            {formatMessageDate(message.timestamp)}\n                            {isSender && (\n                              <CheckCircleIcon\n                                className={`h-3 w-3 ml-1 ${message.read ? 'text-blue-400' : 'text-gray-400'}`}\n                                title={message.read ? 'Okundu' : 'İletildi'}\n                              />\n                            )}\n                          </div>\n                        </div>\n                        {isSender && showAvatar && (\n                          <img \n                            src={message.senderAvatar}\n                            alt={message.senderName}\n                            className=\"h-8 w-8 rounded-full ml-2 mt-1\"\n                          />\n                        )}\n                        {isSender && !showAvatar && <div className=\"w-8 ml-2\"></div>}\n                      </div>\n                    );\n                  })}\n                  <div ref={messagesEndRef} />\n                </div>\n\n                {/* Mesaj Giriş Alanı */}\n                <div className=\"p-3 border-t border-gray-200 bg-white\">\n                  <form onSubmit={handleSendMessage} className=\"flex items-end\">\n                    <button\n                      type=\"button\"\n                      className=\"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\"\n                    >\n                      <PaperClipIcon className=\"h-5 w-5\" />\n                    </button>\n                    <div className=\"flex-1 mx-2\">\n                      <textarea\n                        className=\"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none\"\n                        placeholder=\"Mesajınızı yazın...\"\n                        rows=\"2\"\n                        value={messageText}\n                        onChange={(e) => setMessageText(e.target.value)}\n                        onKeyDown={(e) => {\n                          if (e.key === 'Enter' && !e.shiftKey) {\n                            e.preventDefault();\n                            sendMessage();\n                          }\n                        }}\n                      ></textarea>\n                    </div>\n                    <button\n                      type=\"button\"\n                      className=\"p-2 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none\"\n                    >\n                      <FaceSmileIcon className=\"h-5 w-5\" />\n                    </button>\n                    <button\n                      type=\"submit\"\n                      disabled={!messageText.trim()}\n                      className={`ml-2 p-2 rounded-full ${\n                        messageText.trim() \n                          ? 'bg-indigo-600 text-white hover:bg-indigo-700' \n                          : 'bg-gray-200 text-gray-400 cursor-not-allowed'\n                      } focus:outline-none`}\n                    >\n                      <PaperAirplaneIcon className=\"h-5 w-5\" />\n                    </button>\n                  </form>\n                </div>\n              </>\n            ) : (\n              // Mesaj seçilmediğinde\n              <div className=\"flex-1 flex flex-col items-center justify-center p-6 bg-gray-50\">\n                <div className=\"w-full max-w-md text-center\">\n                  <ChatBubbleLeftEllipsisIcon className=\"h-16 w-16 text-gray-300 mb-4 mx-auto\" />\n                  <h3 className=\"text-xl font-medium text-gray-800 mb-2\">Mesajlarınız</h3>\n                  <p className=\"text-gray-500 mx-auto\">\n                    Mesajlaşmaya başlamak için sol taraftan bir konuşma seçin veya yeni bir mesaj başlatın.\n                  </p>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default MessagesPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,GAAG,MAAM,uBAAuB;AACvC,SAASC,KAAK,QAAQ,iBAAiB;AACvC,OAAOC,EAAE,MAAM,kBAAkB;AACjC,SACEC,0BAA0B,EAC1BC,mBAAmB,EACnBC,QAAQ,EACRC,iBAAiB,EACjBC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,sBAAsB,EACtBC,SAAS,EACTC,eAAe,EACfC,qBAAqB,EACrBC,SAAS,EACTC,eAAe,EACfC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,QAAQ,QACH,6BAA6B;AACpC,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,EAAE,QAAQ,iBAAiB;AACpC,SAASC,IAAI,QAAQ,kBAAkB;;AAEvC;AACA;AACA;AAFA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGA,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC;EAAK,CAAC,GAAG9B,OAAO,CAAC,CAAC;EAC1B,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACoC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACsC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAACwC,QAAQ,EAAEC,WAAW,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8C,MAAM,EAAEC,SAAS,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACgD,MAAM,EAAEC,SAAS,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC,IAAIoD,GAAG,CAAC,CAAC,CAAC;EACzD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtD,QAAQ,CAAC,IAAIoD,GAAG,CAAC,CAAC,CAAC;EACzD,MAAMG,cAAc,GAAGrD,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMsD,gBAAgB,GAAGtD,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMuD,oBAAoB,GAAGvD,MAAM,CAAC,IAAI,CAAC;;EAEzC;EACAD,SAAS,CAAC,MAAM;IACd,IAAIyD,SAAS,GAAG,IAAI;IAEpB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACjD,IAAIF,KAAK,IAAI1B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6B,EAAE,IAAI,EAACd,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEe,SAAS,GAAE;MAC3CC,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAEhC,IAAI,CAAC6B,EAAE,CAAC;MAC7E,MAAMI,gBAAgB,GAAG5D,EAAE,CAAC6D,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,4BAA4B,EAAE;QACzFC,IAAI,EAAE;UAAEX;QAAM,CAAC;QACfY,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;QACpCC,YAAY,EAAE,IAAI;QAClBC,iBAAiB,EAAE,IAAI;QACvBC,oBAAoB,EAAE,CAAC;QACvBC,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE,KAAK;QACfC,kBAAkB,EAAE,KAAK;QAAE;QAC3BC,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE,IAAI;QACbC,eAAe,EAAE;MACnB,CAAC,CAAC;MAEF/B,SAAS,CAACiB,gBAAgB,CAAC;;MAE3B;MACAA,gBAAgB,CAACe,EAAE,CAAC,SAAS,EAAE,MAAM;QACnCjB,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEC,gBAAgB,CAACJ,EAAE,CAAC;QAC3EE,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEC,gBAAgB,CAACH,SAAS,CAAC;QACvEC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEC,gBAAgB,CAACgB,KAAK,CAAC;QAC/D;QACAhB,gBAAgB,CAACiB,IAAI,CAAC,aAAa,CAAC;QACpC;QACAjB,gBAAgB,CAACiB,IAAI,CAAC,kBAAkB,CAAC;MAC3C,CAAC,CAAC;MAEFjB,gBAAgB,CAACe,EAAE,CAAC,YAAY,EAAGG,MAAM,IAAK;QAC5CpB,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEmB,MAAM,CAAC;MAChE,CAAC,CAAC;MAEFlB,gBAAgB,CAACe,EAAE,CAAC,eAAe,EAAGI,KAAK,IAAK;QAC9CrB,OAAO,CAACqB,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC/D,CAAC,CAAC;MAEFnB,gBAAgB,CAACe,EAAE,CAAC,WAAW,EAAGK,aAAa,IAAK;QAClDtB,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEqB,aAAa,CAAC;MAC9E,CAAC,CAAC;MAEFpB,gBAAgB,CAACe,EAAE,CAAC,iBAAiB,EAAGI,KAAK,IAAK;QAChDrB,OAAO,CAACqB,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACvE,CAAC,CAAC;;MAEF;MACA,MAAME,iBAAiB,GAAGC,WAAW,CAAC,MAAM;QAC1C,IAAItB,gBAAgB,CAACH,SAAS,EAAE;UAC9BG,gBAAgB,CAACiB,IAAI,CAAC,MAAM,EAAGM,QAAQ,IAAK;YAC1C,IAAIA,QAAQ,KAAK,MAAM,EAAE;cACvBzB,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;YACxC;UACF,CAAC,CAAC;QACJ;MACF,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;MAEX,OAAO,MAAM;QACXP,SAAS,GAAG,KAAK;QACjBgC,aAAa,CAACH,iBAAiB,CAAC;;QAEhC;QACA,IAAIrB,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEH,SAAS,EAAE;UAC/BG,gBAAgB,CAACyB,GAAG,CAAC,CAAC,CAAC,CAAC;UACxBzB,gBAAgB,CAAC0B,UAAU,CAAC,CAAC;QAC/B;MACF,CAAC;IACH,CAAC,MAAM;MACL5B,OAAO,CAACC,GAAG,CAAC,kEAAkE,EAAE;QAC9E4B,QAAQ,EAAE,CAAC,CAAClC,KAAK;QACjBmC,OAAO,EAAE,CAAC,EAAC7D,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6B,EAAE;QACnBiC,eAAe,EAAE,CAAC,EAAC/C,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEe,SAAS;MACtC,CAAC,CAAC;IACJ;IAEA,OAAO,MAAM;MACXL,SAAS,GAAG,KAAK;IACnB,CAAC;EACH,CAAC,EAAE,CAACzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6B,EAAE,CAAC,CAAC;;EAEd;EACA7D,SAAS,CAAC,MAAM;IACd,IAAI+C,MAAM,IAAIf,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6B,EAAE,EAAE;MACtBE,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;MAErD;MACA,IAAIjB,MAAM,CAACe,SAAS,EAAE;QACpBC,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;QACnEjB,MAAM,CAACmC,IAAI,CAAC,kBAAkB,CAAC;MACjC;;MAEA;MACA,MAAMa,gBAAgB,GAAIC,OAAO,IAAK;QACpCjC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEgC,OAAO,CAAC;QACrDjC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEhC,IAAI,CAAC6B,EAAE,CAAC;QACnDE,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEgC,OAAO,CAACC,QAAQ,CAAC;QAC9DlC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEgC,OAAO,CAACE,cAAc,CAAC;;QAE1E;QACA9D,gBAAgB,CAAC+D,IAAI,IAAI;UACvBpC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UACrD,OAAOmC,IAAI,CAACC,GAAG,CAACC,IAAI,IAClBA,IAAI,CAACxC,EAAE,KAAKmC,OAAO,CAACE,cAAc,GAC9B;YAAE,GAAGG,IAAI;YAAEC,WAAW,EAAEN,OAAO,CAACO,OAAO;YAAEC,SAAS,EAAER,OAAO,CAACS,SAAS;YAAEC,MAAM,EAAEV,OAAO,CAACC,QAAQ,KAAKjE,IAAI,CAAC6B;UAAG,CAAC,GAC7GwC,IACN,CAAC;QACH,CAAC,CAAC;;QAEF;QACA/D,uBAAuB,CAACqE,eAAe,IAAI;UACzC5C,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE2C,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE9C,EAAE,CAAC;UAC7E,IAAI8C,eAAe,IAAIX,OAAO,CAACE,cAAc,KAAKS,eAAe,CAAC9C,EAAE,EAAE;YACpEE,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;YAC/DxB,WAAW,CAAC2D,IAAI,IAAI;cAClB;cACA,MAAMS,aAAa,GAAGT,IAAI,CAACU,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACjD,EAAE,KAAKmC,OAAO,CAACnC,EAAE,CAAC;cAC7D,IAAI+C,aAAa,EAAE;gBACjB7C,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEgC,OAAO,CAACnC,EAAE,CAAC;gBACvE,OAAOsC,IAAI;cACb;cAEA,OAAO,CAAC,GAAGA,IAAI,EAAE;gBACftC,EAAE,EAAEmC,OAAO,CAACnC,EAAE;gBACdoC,QAAQ,EAAED,OAAO,CAACC,QAAQ;gBAC1Bc,UAAU,EAAEf,OAAO,CAACgB,MAAM,CAACC,IAAI;gBAC/BC,YAAY,EAAE,oCAAoCC,kBAAkB,CAACnB,OAAO,CAACgB,MAAM,CAACC,IAAI,CAAC,qDAAqD;gBAC9IG,IAAI,EAAEpB,OAAO,CAACO,OAAO;gBACrBC,SAAS,EAAER,OAAO,CAACS,SAAS;gBAC5BY,IAAI,EAAErB,OAAO,CAACsB,MAAM;gBACpBC,SAAS,EAAE,IAAI,CAAC;cAClB,CAAC,CAAC;YACJ,CAAC,CAAC;;YAEF;YACA,IAAIvB,OAAO,CAACC,QAAQ,KAAKjE,IAAI,CAAC6B,EAAE,EAAE;cAChCE,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC;cACjFwD,UAAU,CAAC,MAAM;gBACfC,sBAAsB,CAACzB,OAAO,CAACE,cAAc,CAAC;cAChD,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;YACX;;YAEA;YACAsB,UAAU,CAAC,MAAM;cAAA,IAAAE,qBAAA;cACf,CAAAA,qBAAA,GAAApE,cAAc,CAACqE,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;gBAAEC,QAAQ,EAAE;cAAS,CAAC,CAAC;YAChE,CAAC,EAAE,GAAG,CAAC;UACT,CAAC,MAAM;YACL9D,OAAO,CAACC,GAAG,CAAC,oFAAoF,CAAC;UACnG;UACA,OAAO2C,eAAe;QACxB,CAAC,CAAC;MACJ,CAAC;MAED,MAAMmB,iBAAiB,GAAI9B,OAAO,IAAK;QACrCjC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEgC,OAAO,CAAC;MACnD,CAAC;MAED,MAAM+B,sBAAsB,GAAIC,IAAI,IAAK;QACvCjE,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEgE,IAAI,CAAC;QACnD9E,cAAc,CAACiD,IAAI,IAAI;UACrB,MAAM8B,MAAM,GAAG,IAAI9E,GAAG,CAACgD,IAAI,CAAC;UAC5B,IAAI6B,IAAI,CAACE,MAAM,KAAK,QAAQ,EAAE;YAC5BD,MAAM,CAACE,GAAG,CAACH,IAAI,CAACI,MAAM,CAAC;UACzB,CAAC,MAAM;YACLH,MAAM,CAACI,MAAM,CAACL,IAAI,CAACI,MAAM,CAAC;UAC5B;UACArE,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEsE,KAAK,CAACC,IAAI,CAACN,MAAM,CAAC,CAAC;UACnE,OAAOA,MAAM;QACf,CAAC,CAAC;MACJ,CAAC;MAED,MAAMO,qBAAqB,GAAIC,OAAO,IAAK;QACzC1E,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEyE,OAAO,CAAC;QAC9DvF,cAAc,CAAC,IAAIC,GAAG,CAACsF,OAAO,CAAC,CAAC;MAClC,CAAC;MAED,MAAMC,gBAAgB,GAAIV,IAAI,IAAK;QACjC1F,uBAAuB,CAACqE,eAAe,IAAI;UACzC,IAAIA,eAAe,IAAIqB,IAAI,CAAC9B,cAAc,KAAKS,eAAe,CAAC9C,EAAE,EAAE;YACjER,cAAc,CAAC8C,IAAI,IAAI,IAAIhD,GAAG,CAAC,CAAC,GAAGgD,IAAI,EAAE6B,IAAI,CAACI,MAAM,CAAC,CAAC,CAAC;UACzD;UACA,OAAOzB,eAAe;QACxB,CAAC,CAAC;MACJ,CAAC;MAED,MAAMgC,uBAAuB,GAAIX,IAAI,IAAK;QACxC3E,cAAc,CAAC8C,IAAI,IAAI;UACrB,MAAM8B,MAAM,GAAG,IAAI9E,GAAG,CAACgD,IAAI,CAAC;UAC5B8B,MAAM,CAACI,MAAM,CAACL,IAAI,CAACI,MAAM,CAAC;UAC1B,OAAOH,MAAM;QACf,CAAC,CAAC;MACJ,CAAC;MAED,MAAMW,kBAAkB,GAAIZ,IAAI,IAAK;QACnCjE,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEgE,IAAI,CAAC;QACrDjE,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEgE,IAAI,CAACa,MAAM,EAAE,eAAe,EAAE7G,IAAI,CAAC6B,EAAE,CAAC;QAC/EE,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEgE,IAAI,CAACc,UAAU,CAAC;;QAEvE;QACA,IAAId,IAAI,CAACa,MAAM,KAAK7G,IAAI,CAAC6B,EAAE,EAAE;UAC3BrB,WAAW,CAAC2D,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACU,GAAG,IAAI;YAClC;YACA,IAAIA,GAAG,CAACb,QAAQ,KAAKjE,IAAI,CAAC6B,EAAE,IAAIiD,GAAG,CAACZ,cAAc,KAAK8B,IAAI,CAAC9B,cAAc,EAAE;cAC1EnC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE8C,GAAG,CAACjD,EAAE,CAAC;cAC9D,OAAO;gBAAE,GAAGiD,GAAG;gBAAEO,IAAI,EAAE;cAAK,CAAC;YAC/B;YACA;YACA,IAAIW,IAAI,CAACc,UAAU,CAACC,QAAQ,CAACjC,GAAG,CAACjD,EAAE,CAAC,EAAE;cACpCE,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAE8C,GAAG,CAACjD,EAAE,CAAC;cACpE,OAAO;gBAAE,GAAGiD,GAAG;gBAAEO,IAAI,EAAE;cAAK,CAAC;YAC/B;YACA,OAAOP,GAAG;UACZ,CAAC,CAAC,CAAC;;UAEH;UACA1E,gBAAgB,CAAC+D,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IACpCA,IAAI,CAACxC,EAAE,KAAKmE,IAAI,CAAC9B,cAAc,GAC3B;YAAE,GAAGG,IAAI;YAAEK,MAAM,EAAE;UAAM,CAAC,GAC1BL,IACN,CAAC,CAAC;QACJ;MACF,CAAC;;MAED;MACAtD,MAAM,CAACiC,EAAE,CAAC,aAAa,EAAEe,gBAAgB,CAAC;MAC1ChD,MAAM,CAACiC,EAAE,CAAC,cAAc,EAAE8C,iBAAiB,CAAC;MAC5C/E,MAAM,CAACiC,EAAE,CAAC,oBAAoB,EAAE+C,sBAAsB,CAAC;MACvDhF,MAAM,CAACiC,EAAE,CAAC,aAAa,EAAE0D,gBAAgB,CAAC;MAC1C3F,MAAM,CAACiC,EAAE,CAAC,qBAAqB,EAAE2D,uBAAuB,CAAC;MACzD5F,MAAM,CAACiC,EAAE,CAAC,eAAe,EAAE4D,kBAAkB,CAAC;MAC9C7F,MAAM,CAACiC,EAAE,CAAC,mBAAmB,EAAEwD,qBAAqB,CAAC;;MAErD;MACA,OAAO,MAAM;QACXzF,MAAM,CAAC2C,GAAG,CAAC,aAAa,EAAEK,gBAAgB,CAAC;QAC3ChD,MAAM,CAAC2C,GAAG,CAAC,cAAc,EAAEoC,iBAAiB,CAAC;QAC7C/E,MAAM,CAAC2C,GAAG,CAAC,oBAAoB,EAAEqC,sBAAsB,CAAC;QACxDhF,MAAM,CAAC2C,GAAG,CAAC,aAAa,EAAEgD,gBAAgB,CAAC;QAC3C3F,MAAM,CAAC2C,GAAG,CAAC,qBAAqB,EAAEiD,uBAAuB,CAAC;QAC1D5F,MAAM,CAAC2C,GAAG,CAAC,eAAe,EAAEkD,kBAAkB,CAAC;QAC/C7F,MAAM,CAAC2C,GAAG,CAAC,mBAAmB,EAAE8C,qBAAqB,CAAC;MACxD,CAAC;IACH;EACF,CAAC,EAAE,CAACzF,MAAM,EAAEf,IAAI,CAAC6B,EAAE,CAAC,CAAC;;EAErB;EACA7D,SAAS,CAAC,MAAM;IACdgJ,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGnJ,QAAQ,CAAC,IAAIoD,GAAG,CAAC,CAAC,CAAC;EAEzEnD,SAAS,CAAC,MAAM;IACd,IAAI+C,MAAM,IAAIZ,aAAa,CAACgH,MAAM,GAAG,CAAC,EAAE;MACtCpF,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE7B,aAAa,CAACgH,MAAM,EAAE,cAAc,CAAC;MAEnGhH,aAAa,CAACiH,OAAO,CAACC,YAAY,IAAI;QACpC,IAAI,CAACJ,mBAAmB,CAACK,GAAG,CAACD,YAAY,CAACxF,EAAE,CAAC,EAAE;UAC7Cd,MAAM,CAACmC,IAAI,CAAC,mBAAmB,EAAEmE,YAAY,CAACxF,EAAE,CAAC;UACjDE,OAAO,CAACC,GAAG,CAAC,2BAA2BqF,YAAY,CAACxF,EAAE,UAAU,CAAC;UACjEqF,sBAAsB,CAAC/C,IAAI,IAAI,IAAIhD,GAAG,CAAC,CAAC,GAAGgD,IAAI,EAAEkD,YAAY,CAACxF,EAAE,CAAC,CAAC,CAAC;QACrE;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACd,MAAM,EAAEZ,aAAa,EAAE8G,mBAAmB,CAAC,CAAC;;EAEhD;EACA,MAAMD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF9G,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMsD,QAAQ,GAAG,MAAMrF,GAAG,CAACoJ,GAAG,CAAC,yBAAyB,CAAC;;MAEzD;MACA,MAAMC,sBAAsB,GAAGhE,QAAQ,CAACwC,IAAI,CAAC7F,aAAa,CAACiE,GAAG,CAACiD,YAAY;QAAA,IAAAI,qBAAA,EAAAC,sBAAA;QAAA,OAAK;UAC9E7F,EAAE,EAAEwF,YAAY,CAACxF,EAAE;UACnB8F,QAAQ,EAAEN,YAAY,CAACO,SAAS,CAAC/F,EAAE;UACnCgG,UAAU,EAAER,YAAY,CAACO,SAAS,CAAC3C,IAAI;UACvCX,WAAW,EAAE,EAAAmD,qBAAA,GAAAJ,YAAY,CAAC/C,WAAW,cAAAmD,qBAAA,uBAAxBA,qBAAA,CAA0BlD,OAAO,KAAI,iBAAiB;UACnEC,SAAS,EAAE,EAAAkD,sBAAA,GAAAL,YAAY,CAAC/C,WAAW,cAAAoD,sBAAA,uBAAxBA,sBAAA,CAA0BlD,SAAS,KAAI6C,YAAY,CAAC5C,SAAS;UACxEC,MAAM,EAAE2C,YAAY,CAAC/C,WAAW,GAAG,CAAC+C,YAAY,CAAC/C,WAAW,CAACgB,MAAM,IAAI+B,YAAY,CAAC/C,WAAW,CAACL,QAAQ,KAAKjE,IAAI,CAAC6B,EAAE,GAAG,KAAK;UAC5HiG,MAAM,EAAE,oCAAoC3C,kBAAkB,CAACkC,YAAY,CAACO,SAAS,CAAC3C,IAAI,CAAC,qDAAqD;UAChJiB,MAAM,EAAEjF,WAAW,CAACqG,GAAG,CAACD,YAAY,CAACO,SAAS,CAAC/F,EAAE,CAAC,GAAG,QAAQ,GAAG,SAAS;UACzEkG,OAAO,EAAE,KAAK;UACdC,QAAQ,EAAE;QACZ,CAAC;MAAA,CAAC,CAAC;MAEH5H,gBAAgB,CAACoH,sBAAsB,CAAC;IAC1C,CAAC,CAAC,OAAOpE,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDhF,KAAK,CAACgF,KAAK,CAAC,wBAAwB,CAAC;IACvC,CAAC,SAAS;MACRlD,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACAlC,SAAS,CAAC,MAAM;IACd,IAAIqC,oBAAoB,EAAE;MACxB4H,YAAY,CAAC5H,oBAAoB,CAACwB,EAAE,CAAC;MACrC4D,sBAAsB,CAACpF,oBAAoB,CAACwB,EAAE,CAAC;;MAE/C;MACAzB,gBAAgB,CAAC+D,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IACpCA,IAAI,CAACxC,EAAE,KAAKxB,oBAAoB,CAACwB,EAAE,GAC/B;QAAE,GAAGwC,IAAI;QAAEK,MAAM,EAAE;MAAM,CAAC,GAC1BL,IACN,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAChE,oBAAoB,CAAC,CAAC;;EAE1B;EACA,MAAMoF,sBAAsB,GAAG,MAAOvB,cAAc,IAAK;IACvD,IAAI;MACF;MACA,MAAMV,QAAQ,GAAG,MAAMrF,GAAG,CAAC+J,GAAG,CAAC,2BAA2BhE,cAAc,OAAO,CAAC;MAEhF,IAAIV,QAAQ,CAACwC,IAAI,CAACmC,YAAY,GAAG,CAAC,EAAE;QAClCpG,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEwB,QAAQ,CAACwC,IAAI,CAACmC,YAAY,EAAE,kBAAkB,CAAC;;QAEhF;QACA3H,WAAW,CAAC2D,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACU,GAAG,IAC9BA,GAAG,CAACb,QAAQ,KAAKjE,IAAI,CAAC6B,EAAE,GAAG;UAAE,GAAGiD,GAAG;UAAEO,IAAI,EAAE;QAAK,CAAC,GAAGP,GACtD,CAAC,CAAC;;QAEF;QACA,IAAI/D,MAAM,EAAE;UACVA,MAAM,CAACmC,IAAI,CAAC,oBAAoB,EAAE;YAChCgB,cAAc;YACd4C,UAAU,EAAE,EAAE,CAAC;UACjB,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC,OAAO1D,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;IACpE;EACF,CAAC;;EAED;EACA,MAAM6E,YAAY,GAAG,MAAO/D,cAAc,IAAK;IAC7C,IAAI;MACF,MAAMV,QAAQ,GAAG,MAAMrF,GAAG,CAACoJ,GAAG,CAAC,2BAA2BrD,cAAc,EAAE,CAAC;;MAE3E;MACA,MAAMkE,iBAAiB,GAAG5E,QAAQ,CAACwC,IAAI,CAACzF,QAAQ,CAAC6D,GAAG,CAACJ,OAAO,KAAK;QAC/DnC,EAAE,EAAEmC,OAAO,CAACnC,EAAE;QACdoC,QAAQ,EAAED,OAAO,CAACC,QAAQ;QAC1Bc,UAAU,EAAEf,OAAO,CAACgB,MAAM,CAACC,IAAI;QAC/BC,YAAY,EAAE,oCAAoCC,kBAAkB,CAACnB,OAAO,CAACgB,MAAM,CAACC,IAAI,CAAC,qDAAqD;QAC9IG,IAAI,EAAEpB,OAAO,CAACO,OAAO;QACrBC,SAAS,EAAER,OAAO,CAACS,SAAS;QAC5BY,IAAI,EAAErB,OAAO,CAACsB,MAAM;QACpBC,SAAS,EAAE,IAAI;QAAE;QACjB8C,WAAW,EAAErE,OAAO,CAACqE;MACvB,CAAC,CAAC,CAAC;MAEH7H,WAAW,CAAC4H,iBAAiB,CAAC;;MAE9B;MACA5C,UAAU,CAAC,MAAM;QAAA,IAAA8C,sBAAA;QACf,CAAAA,sBAAA,GAAAhH,cAAc,CAACqE,OAAO,cAAA2C,sBAAA,uBAAtBA,sBAAA,CAAwB1C,cAAc,CAAC;UAAEC,QAAQ,EAAE;QAAS,CAAC,CAAC;MAChE,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAOzC,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDhF,KAAK,CAACgF,KAAK,CAAC,sBAAsB,CAAC;IACrC;EACF,CAAC;EACD;EACA,MAAMmF,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAAC9H,WAAW,CAAC+H,IAAI,CAAC,CAAC,IAAI,CAACnI,oBAAoB,EAAE;IAElD0B,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;MACpCyG,UAAU,EAAEpI,oBAAoB,CAACsH,QAAQ;MACzCpD,OAAO,EAAE9D,WAAW,CAAC+H,IAAI,CAAC;IAC5B,CAAC,CAAC;IAEF,IAAI;MACF,MAAMhF,QAAQ,GAAG,MAAMrF,GAAG,CAACuK,IAAI,CAAC,gBAAgB,EAAE;QAChDD,UAAU,EAAEpI,oBAAoB,CAACsH,QAAQ;QACzCpD,OAAO,EAAE9D,WAAW,CAAC+H,IAAI,CAAC;MAC5B,CAAC,CAAC;MAEFzG,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEwB,QAAQ,CAACwC,IAAI,CAAC;;MAEjD;MACAtF,cAAc,CAAC,EAAE,CAAC;IAEpB,CAAC,CAAC,OAAO0C,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDhF,KAAK,CAACgF,KAAK,CAAC,qBAAqB,CAAC;IACpC;EACF,CAAC;EAEDpF,SAAS,CAAC,MAAM;IACd;IACA,IAAIsD,cAAc,CAACqE,OAAO,IAAInE,oBAAoB,CAACmE,OAAO,EAAE;MAC1DnE,oBAAoB,CAACmE,OAAO,CAACgD,SAAS,GAAGnH,oBAAoB,CAACmE,OAAO,CAACiD,YAAY;IACpF;EACF,CAAC,EAAE,CAACrI,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMsI,iBAAiB,GAAIC,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBR,WAAW,CAAC,CAAC;EACf,CAAC;;EAED;EACA,MAAMS,wBAAwB,GAAI3B,YAAY,IAAK;IACjD/G,uBAAuB,CAAC+G,YAAY,CAAC;EACvC,CAAC;;EAED;EACA,MAAM4B,iBAAiB,GAAIC,UAAU,IAAK;IACxC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,KAAK,GAAG,IAAID,IAAI,CAAC,CAAC;IACxB,MAAME,SAAS,GAAG,IAAIF,IAAI,CAACC,KAAK,CAAC;IACjCC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IAE1C,IAAIL,IAAI,CAACM,YAAY,CAAC,CAAC,KAAKJ,KAAK,CAACI,YAAY,CAAC,CAAC,EAAE;MAChD,OAAOlK,MAAM,CAAC4J,IAAI,EAAE,OAAO,EAAE;QAAEO,MAAM,EAAElK;MAAG,CAAC,CAAC;IAC9C,CAAC,MAAM,IAAI2J,IAAI,CAACM,YAAY,CAAC,CAAC,KAAKH,SAAS,CAACG,YAAY,CAAC,CAAC,EAAE;MAC3D,OAAO,MAAM,GAAGlK,MAAM,CAAC4J,IAAI,EAAE,OAAO,EAAE;QAAEO,MAAM,EAAElK;MAAG,CAAC,CAAC;IACvD,CAAC,MAAM;MACL,OAAOD,MAAM,CAAC4J,IAAI,EAAE,cAAc,EAAE;QAAEO,MAAM,EAAElK;MAAG,CAAC,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMmK,qBAAqB,GAAGxJ,aAAa,CAACU,MAAM,CAACwD,IAAI,IAAI;IACzD;IACA,MAAMuF,aAAa,GAAGvF,IAAI,CAACwD,UAAU,CAACgC,WAAW,CAAC,CAAC,CAAC9C,QAAQ,CAACpG,UAAU,CAACkJ,WAAW,CAAC,CAAC,CAAC,IACjExF,IAAI,CAACC,WAAW,CAACuF,WAAW,CAAC,CAAC,CAAC9C,QAAQ,CAACpG,UAAU,CAACkJ,WAAW,CAAC,CAAC,CAAC;;IAEtF;IACA,MAAMC,aAAa,GAAGjJ,MAAM,KAAK,KAAK,IAChBA,MAAM,KAAK,QAAQ,IAAIwD,IAAI,CAACK,MAAO,IACnC7D,MAAM,KAAK,UAAU,IAAIwD,IAAI,CAAC2D,QAAS,IACvCnH,MAAM,KAAK,SAAS,IAAIwD,IAAI,CAAC0D,OAAQ;IAE3D,OAAO6B,aAAa,IAAIE,aAAa;EACvC,CAAC,CAAC;;EAEF;EACA,MAAMC,UAAU,GAAIlI,EAAE,IAAK;IACzBzB,gBAAgB,CAAC4J,iBAAiB,IAChCA,iBAAiB,CAAC5F,GAAG,CAACC,IAAI,IACxBA,IAAI,CAACxC,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGwC,IAAI;MAAE0D,OAAO,EAAE,CAAC1D,IAAI,CAAC0D;IAAQ,CAAC,GAAG1D,IACzD,CACF,CAAC;IAED,IAAIhE,oBAAoB,IAAIA,oBAAoB,CAACwB,EAAE,KAAKA,EAAE,EAAE;MAC1DvB,uBAAuB,CAAC6D,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE4D,OAAO,EAAE,CAAC5D,IAAI,CAAC4D;MAAQ,CAAC,CAAC,CAAC;IACxE;EACF,CAAC;;EAED;EACA,MAAMkC,aAAa,GAAIpI,EAAE,IAAK;IAC5BzB,gBAAgB,CAAC4J,iBAAiB,IAChCA,iBAAiB,CAAC5F,GAAG,CAACC,IAAI,IACxBA,IAAI,CAACxC,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGwC,IAAI;MAAE2D,QAAQ,EAAE,CAAC3D,IAAI,CAAC2D;IAAS,CAAC,GAAG3D,IAC3D,CACF,CAAC;IAED,IAAIhE,oBAAoB,IAAIA,oBAAoB,CAACwB,EAAE,KAAKA,EAAE,EAAE;MAC1DvB,uBAAuB,CAAC6D,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE6D,QAAQ,EAAE,CAAC7D,IAAI,CAAC6D;MAAS,CAAC,CAAC,CAAC;IAC1E;EACF,CAAC;EAED,IAAI/H,SAAS,EAAE;IACb,oBACEN,OAAA;MAAKuK,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5DxK,OAAA;QAAKuK,SAAS,EAAC;MAA6E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChG,CAAC;EAEV;EAEA,oBACE5K,OAAA;IAAKuK,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAE1CxK,OAAA;MAAKuK,SAAS,EAAC,qFAAqF;MAAAC,QAAA,eAClGxK,OAAA;QAAKuK,SAAS,EAAC,uEAAuE;QAAAC,QAAA,gBACpFxK,OAAA;UAAAwK,QAAA,gBACExK,OAAA;YAAIuK,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClD5K,OAAA;YAAGuK,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAC;UAEpC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACN5K,OAAA;UAAKuK,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC1CxK,OAAA;YAAQuK,SAAS,EAAC,6NAA6N;YAAAC,QAAA,gBAC7OxK,OAAA,CAACrB,0BAA0B;cAAC4L,SAAS,EAAC,oBAAoB;cAAC,eAAY;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAElF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5K,OAAA;MAAKuK,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5DxK,OAAA;QAAKuK,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBAEzCxK,OAAA;UAAKuK,SAAS,EAAC,kEAAkE;UAAAC,QAAA,gBAC/ExK,OAAA;YAAKuK,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDxK,OAAA;cAAIuK,SAAS,EAAC,uDAAuD;cAAAC,QAAA,gBACnExK,OAAA,CAACrB,0BAA0B;gBAAC4L,SAAS,EAAC;cAA8B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAEzE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL5K,OAAA;cAAKuK,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BxK,OAAA;gBACE6K,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,0BAAqB;gBACjCP,SAAS,EAAC,qHAAqH;gBAC/HQ,KAAK,EAAE/J,UAAW;gBAClBgK,QAAQ,EAAG7B,CAAC,IAAKlI,aAAa,CAACkI,CAAC,CAAC8B,MAAM,CAACF,KAAK;cAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACF5K,OAAA,CAACpB,mBAAmB;gBAAC2L,SAAS,EAAC;cAA+C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC,eACN5K,OAAA;cAAKuK,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAClCxK,OAAA;gBACEuK,SAAS,EAAE,kCAAkCrJ,MAAM,KAAK,KAAK,GACzD,+BAA+B,GAC/B,6CAA6C,EAAG;gBACpDgK,OAAO,EAAEA,CAAA,KAAM/J,SAAS,CAAC,KAAK,CAAE;gBAAAqJ,QAAA,EACjC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT5K,OAAA;gBACEuK,SAAS,EAAE,kCAAkCrJ,MAAM,KAAK,QAAQ,GAC5D,+BAA+B,GAC/B,6CAA6C,EAAG;gBACpDgK,OAAO,EAAEA,CAAA,KAAM/J,SAAS,CAAC,QAAQ,CAAE;gBAAAqJ,QAAA,EACpC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT5K,OAAA;gBACEuK,SAAS,EAAE,kCAAkCrJ,MAAM,KAAK,SAAS,GAC7D,+BAA+B,GAC/B,6CAA6C,EAAG;gBACpDgK,OAAO,EAAEA,CAAA,KAAM/J,SAAS,CAAC,SAAS,CAAE;gBAAAqJ,QAAA,EACrC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT5K,OAAA;gBACEuK,SAAS,EAAE,kCAAkCrJ,MAAM,KAAK,UAAU,GAC9D,+BAA+B,GAC/B,6CAA6C,EAAG;gBACpDgK,OAAO,EAAEA,CAAA,KAAM/J,SAAS,CAAC,UAAU,CAAE;gBAAAqJ,QAAA,EACtC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN5K,OAAA;YACEmL,GAAG,EAAEvJ,gBAAiB;YACtBwJ,KAAK,EAAE;cACLC,MAAM,EAAE,oBAAoB;cAC5BC,SAAS,EAAE,MAAM;cACjBC,cAAc,EAAE,MAAM;cACtBC,cAAc,EAAE;YAClB,CAAE;YAAAhB,QAAA,EAEDR,qBAAqB,CAACxC,MAAM,KAAK,CAAC,gBACjCxH,OAAA;cAAKuK,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAE/C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAENZ,qBAAqB,CAACvF,GAAG,CAACiD,YAAY,iBACpC1H,OAAA;cAEEuK,SAAS,EAAE,sEACT,CAAA7J,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEwB,EAAE,MAAKwF,YAAY,CAACxF,EAAE,GAAG,cAAc,GAAG,EAAE,IAChEwF,YAAY,CAAC3C,MAAM,GAAG,kCAAkC,GAAG,EAAE,EAAG;cACpEmG,OAAO,EAAEA,CAAA,KAAM7B,wBAAwB,CAAC3B,YAAY,CAAE;cAAA8C,QAAA,gBAEtDxK,OAAA;gBAAKuK,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzCxK,OAAA;kBAAKuK,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCxK,OAAA;oBACEyL,GAAG,EAAE/D,YAAY,CAACS,MAAO;oBACzBuD,GAAG,EAAEhE,YAAY,CAACQ,UAAW;oBAC7BqC,SAAS,EAAE,0BACT,CAAA7J,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEwB,EAAE,MAAKwF,YAAY,CAACxF,EAAE,GACxC,wBAAwB,GACxB,EAAE;kBACL;oBAAAuI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,EACDlD,YAAY,CAACnB,MAAM,KAAK,QAAQ,iBAC/BvG,OAAA;oBAAMuK,SAAS,EAAC;kBAAyF;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CACjH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACN5K,OAAA;kBAAKuK,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BxK,OAAA;oBAAKuK,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,gBAC/CxK,OAAA;sBAAIuK,SAAS,EAAE,uBAAuB7C,YAAY,CAAC3C,MAAM,GAAG,eAAe,GAAG,eAAe,EAAG;sBAAAyF,QAAA,EAC7F9C,YAAY,CAACQ;oBAAU;sBAAAuC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB,CAAC,eACL5K,OAAA;sBAAKuK,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,GACzC9C,YAAY,CAACU,OAAO,iBACnBpI,OAAA,CAACL,QAAQ;wBAAC4K,SAAS,EAAC;sBAAsC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAC7D,eACD5K,OAAA;wBAAMuK,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EACpClB,iBAAiB,CAAC5B,YAAY,CAAC7C,SAAS;sBAAC;wBAAA4F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN5K,OAAA;oBAAGuK,SAAS,EAAE,yBACZ7C,YAAY,CAAC3C,MAAM,GAAG,2BAA2B,GAAG,eAAe,EAClE;oBAAAyF,QAAA,EACA9C,YAAY,CAAC/C;kBAAW;oBAAA8F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eACJ5K,OAAA;oBAAKuK,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrDxK,OAAA;sBAAMuK,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EACpClJ,WAAW,CAACqG,GAAG,CAACD,YAAY,CAACM,QAAQ,CAAC,gBACnChI,OAAA;wBAAMuK,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,EAAC;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,GAC7DlD,YAAY,CAACiE,QAAQ,GACnB,gBAAgBjE,YAAY,CAACiE,QAAQ,EAAE,GACvC;oBAAE;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACP5K,OAAA;sBAAKuK,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,gBAC7BxK,OAAA;wBACEkL,OAAO,EAAG/B,CAAC,IAAK;0BACdA,CAAC,CAACyC,eAAe,CAAC,CAAC;0BACnBxB,UAAU,CAAC1C,YAAY,CAACxF,EAAE,CAAC;wBAC7B,CAAE;wBACFqI,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,eAE/CxK,OAAA,CAACL,QAAQ;0BAAC4K,SAAS,EAAE,WAAW7C,YAAY,CAACU,OAAO,GAAG,8BAA8B,GAAG,EAAE;wBAAG;0BAAAqC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1F,CAAC,eACT5K,OAAA;wBACEkL,OAAO,EAAG/B,CAAC,IAAK;0BACdA,CAAC,CAACyC,eAAe,CAAC,CAAC;0BACnBtB,aAAa,CAAC5C,YAAY,CAACxF,EAAE,CAAC;wBAChC,CAAE;wBACFqI,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,eAE7CxK,OAAA,CAACN,cAAc;0BAAC6K,SAAS,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACLlD,YAAY,CAAC3C,MAAM,iBAClB/E,OAAA;gBAAMuK,SAAS,EAAC,2GAA2G;gBAAAC,QAAA,EAAC;cAE5H;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP;YAAA,GA3EIlD,YAAY,CAACxF,EAAE;cAAAuI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA4EjB,CACN;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN5K,OAAA;UAAKuK,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EACrD9J,oBAAoB,gBACnBV,OAAA,CAAAE,SAAA;YAAAsK,QAAA,gBAEExK,OAAA;cAAKuK,SAAS,EAAC,yEAAyE;cAAAC,QAAA,gBACtFxK,OAAA;gBAAKuK,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCxK,OAAA;kBAAKuK,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BxK,OAAA;oBACEyL,GAAG,EAAE/K,oBAAoB,CAACyH,MAAO;oBACjCuD,GAAG,EAAEhL,oBAAoB,CAACwH,UAAW;oBACrCqC,SAAS,EAAC;kBAAwB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC,EACDlK,oBAAoB,CAAC6F,MAAM,KAAK,QAAQ,iBACvCvG,OAAA;oBAAMuK,SAAS,EAAC;kBAAyF;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CACjH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACN5K,OAAA;kBAAAwK,QAAA,gBACExK,OAAA;oBAAIuK,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAC9C9J,oBAAoB,CAACwH;kBAAU;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACL5K,OAAA;oBAAGuK,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EACjC9J,oBAAoB,CAAC6F,MAAM,KAAK,QAAQ,GACrC,WAAW,GACX7F,oBAAoB,CAACiL,QAAQ,GAC3B,gBAAgBjL,oBAAoB,CAACiL,QAAQ,EAAE,GAC/C;kBAAE;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN5K,OAAA;gBAAKuK,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CxK,OAAA;kBAAQuK,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,eAClExK,OAAA,CAACb,SAAS;oBAACoL,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACT5K,OAAA;kBAAQuK,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,eAClExK,OAAA,CAACZ,eAAe;oBAACmL,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACT5K,OAAA;kBAAQuK,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,eAClExK,OAAA,CAACX,qBAAqB;oBAACkL,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACT5K,OAAA;kBAAQuK,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,eAClExK,OAAA,CAACd,sBAAsB;oBAACqL,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN5K,OAAA;cACEmL,GAAG,EAAEtJ,oBAAqB;cAC1B0I,SAAS,EAAC,gBAAgB;cAC1Ba,KAAK,EAAE;gBACLC,MAAM,EAAE,oBAAoB;gBAC5BC,SAAS,EAAE,MAAM;gBACjBC,cAAc,EAAE,MAAM;gBACtBC,cAAc,EAAE;cAClB,CAAE;cAAAhB,QAAA,GAED5J,QAAQ,CAAC6D,GAAG,CAAC,CAACJ,OAAO,EAAEwH,KAAK,KAAK;gBAChC,MAAMC,QAAQ,GAAGzH,OAAO,CAACC,QAAQ,KAAKjE,IAAI,CAAC6B,EAAE;gBAC7C,MAAM6J,UAAU,GAAGF,KAAK,KAAK,CAAC,IAAIjL,QAAQ,CAACiL,KAAK,GAAG,CAAC,CAAC,CAACvH,QAAQ,KAAKD,OAAO,CAACC,QAAQ;gBAEnF,oBACEtE,OAAA;kBAAsBuK,SAAS,EAAE,QAAQuB,QAAQ,GAAG,aAAa,GAAG,eAAe,OAAQ;kBAAAtB,QAAA,GACxF,CAACsB,QAAQ,IAAIC,UAAU,iBACtB/L,OAAA;oBACEyL,GAAG,EAAEpH,OAAO,CAACkB,YAAa;oBAC1BmG,GAAG,EAAErH,OAAO,CAACe,UAAW;oBACxBmF,SAAS,EAAC;kBAAgC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CACF,EACA,CAACkB,QAAQ,IAAI,CAACC,UAAU,iBAAI/L,OAAA;oBAAKuK,SAAS,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7D5K,OAAA;oBACEuK,SAAS,EAAE,yDACTuB,QAAQ,GACJ,0CAA0C,GAC1C,+DAA+D,EAClE;oBAAAtB,QAAA,gBAEHxK,OAAA;sBAAGuK,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAEnG,OAAO,CAACoB;oBAAI;sBAAAgF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzC5K,OAAA;sBAAKuK,SAAS,EAAE,gBAAgBuB,QAAQ,GAAG,iBAAiB,GAAG,eAAe,gCAAiC;sBAAAtB,QAAA,GAC5GlB,iBAAiB,CAACjF,OAAO,CAACQ,SAAS,CAAC,EACpCiH,QAAQ,iBACP9L,OAAA,CAACT,eAAe;wBACdgL,SAAS,EAAE,gBAAgBlG,OAAO,CAACqB,IAAI,GAAG,eAAe,GAAG,eAAe,EAAG;wBAC9EsG,KAAK,EAAE3H,OAAO,CAACqB,IAAI,GAAG,QAAQ,GAAG;sBAAW;wBAAA+E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7C,CACF;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EACLkB,QAAQ,IAAIC,UAAU,iBACrB/L,OAAA;oBACEyL,GAAG,EAAEpH,OAAO,CAACkB,YAAa;oBAC1BmG,GAAG,EAAErH,OAAO,CAACe,UAAW;oBACxBmF,SAAS,EAAC;kBAAgC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CACF,EACAkB,QAAQ,IAAI,CAACC,UAAU,iBAAI/L,OAAA;oBAAKuK,SAAS,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,GAlCpDvG,OAAO,CAACnC,EAAE;kBAAAuI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAmCf,CAAC;cAEV,CAAC,CAAC,eACF5K,OAAA;gBAAKmL,GAAG,EAAExJ;cAAe;gBAAA8I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eAGN5K,OAAA;cAAKuK,SAAS,EAAC,uCAAuC;cAAAC,QAAA,eACpDxK,OAAA;gBAAMiM,QAAQ,EAAE/C,iBAAkB;gBAACqB,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC3DxK,OAAA;kBACE6K,IAAI,EAAC,QAAQ;kBACbN,SAAS,EAAC,uEAAuE;kBAAAC,QAAA,eAEjFxK,OAAA,CAACjB,aAAa;oBAACwL,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACT5K,OAAA;kBAAKuK,SAAS,EAAC,aAAa;kBAAAC,QAAA,eAC1BxK,OAAA;oBACEuK,SAAS,EAAC,qHAAqH;oBAC/HO,WAAW,EAAC,yCAAqB;oBACjCoB,IAAI,EAAC,GAAG;oBACRnB,KAAK,EAAEjK,WAAY;oBACnBkK,QAAQ,EAAG7B,CAAC,IAAKpI,cAAc,CAACoI,CAAC,CAAC8B,MAAM,CAACF,KAAK,CAAE;oBAChDoB,SAAS,EAAGhD,CAAC,IAAK;sBAChB,IAAIA,CAAC,CAACiD,GAAG,KAAK,OAAO,IAAI,CAACjD,CAAC,CAACkD,QAAQ,EAAE;wBACpClD,CAAC,CAACC,cAAc,CAAC,CAAC;wBAClBR,WAAW,CAAC,CAAC;sBACf;oBACF;kBAAE;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACN5K,OAAA;kBACE6K,IAAI,EAAC,QAAQ;kBACbN,SAAS,EAAC,uEAAuE;kBAAAC,QAAA,eAEjFxK,OAAA,CAAChB,aAAa;oBAACuL,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACT5K,OAAA;kBACE6K,IAAI,EAAC,QAAQ;kBACbyB,QAAQ,EAAE,CAACxL,WAAW,CAAC+H,IAAI,CAAC,CAAE;kBAC9B0B,SAAS,EAAE,yBACTzJ,WAAW,CAAC+H,IAAI,CAAC,CAAC,GACd,8CAA8C,GAC9C,8CAA8C,qBAC9B;kBAAA2B,QAAA,eAEtBxK,OAAA,CAAClB,iBAAiB;oBAACyL,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,eACN,CAAC;UAAA;UAEH;UACA5K,OAAA;YAAKuK,SAAS,EAAC,iEAAiE;YAAAC,QAAA,eAC9ExK,OAAA;cAAKuK,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CxK,OAAA,CAACrB,0BAA0B;gBAAC4L,SAAS,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/E5K,OAAA;gBAAIuK,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxE5K,OAAA;gBAAGuK,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAErC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxK,EAAA,CAh0BID,YAAY;EAAA,QACC5B,OAAO;AAAA;AAAAgO,EAAA,GADpBpM,YAAY;AAk0BlB,eAAeA,YAAY;AAAC,IAAAoM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}