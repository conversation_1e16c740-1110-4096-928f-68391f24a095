{"ast": null, "code": "var _jsxFileName = \"C:\\\\Projeler\\\\kidgarden\\\\burky_root_web\\\\client\\\\src\\\\components\\\\notifications\\\\NotificationCenter.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { BellIcon, XMarkIcon, CheckIcon, ClockIcon, ChatBubbleLeftRightIcon, CalendarIcon, UserIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';\nimport { BellIcon as BellSolidIcon } from '@heroicons/react/24/solid';\nimport { format, formatDistanceToNow } from 'date-fns';\nimport { tr } from 'date-fns/locale';\nimport api from '../../services/api';\nimport toast from 'react-hot-toast';\nimport io from 'socket.io-client';\nimport { useAuth } from '../../hooks/useAuth';\nimport { useNavigate } from 'react-router-dom';\n\n/**\n * Bildirim Merkezi Komponenti\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst NotificationCenter = () => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [notifications, setNotifications] = useState([]);\n  const [unreadCount, setUnreadCount] = useState(0);\n  const [buttonRef, setButtonRef] = useState(null);\n  const [socket, setSocket] = useState(null);\n  const {\n    user\n  } = useAuth();\n  const navigate = useNavigate();\n\n  // Load notifications from API\n  useEffect(() => {\n    loadNotifications();\n    loadUnreadCount();\n  }, []);\n\n  // Socket connection\n  useEffect(() => {\n    let isMounted = true;\n    const token = localStorage.getItem('accessToken');\n    if (token && user !== null && user !== void 0 && user.id && !(socket !== null && socket !== void 0 && socket.connected)) {\n      console.log('🔌 NOTIFICATIONS: Socket.IO bağlantısı kuruluyor... User ID:', user.id);\n      const socketConnection = io(process.env.REACT_APP_API_URL || 'https://192.168.1.111:5000', {\n        auth: {\n          token\n        },\n        transports: ['polling', 'websocket'],\n        // Polling'i önce dene\n        reconnection: true,\n        reconnectionDelay: 2000,\n        reconnectionAttempts: 3,\n        timeout: 15000,\n        forceNew: false,\n        upgrade: false,\n        // WebSocket upgrade'i devre dışı bırak\n        rememberUpgrade: false\n      });\n      setSocket(socketConnection);\n\n      // Connection events\n      socketConnection.on('connect', () => {\n        console.log('✅ NOTIFICATIONS: Socket.IO bağlantısı kuruldu:', socketConnection.id);\n      });\n      socketConnection.on('disconnect', reason => {\n        console.log('❌ NOTIFICATIONS: Socket.IO bağlantısı kesildi:', reason);\n      });\n      return () => {\n        isMounted = false;\n\n        // Güvenli disconnect\n        if (socketConnection !== null && socketConnection !== void 0 && socketConnection.connected) {\n          socketConnection.off(); // Tüm event listener'ları kaldır\n          socketConnection.disconnect();\n        }\n      };\n    } else {\n      console.log('❌ NOTIFICATIONS: Socket bağlantısı kurulamıyor - token veya user eksik:', {\n        hasToken: !!token,\n        hasUser: !!(user !== null && user !== void 0 && user.id),\n        socketConnected: !!(socket !== null && socket !== void 0 && socket.connected)\n      });\n    }\n    return () => {\n      isMounted = false;\n    };\n  }, [user === null || user === void 0 ? void 0 : user.id]);\n\n  // Socket listener for new notifications\n  useEffect(() => {\n    if (!socket) return;\n    const handleNewNotification = notification => {\n      console.log('🔔 New notification received:', notification);\n\n      // Add new notification to the list\n      setNotifications(prev => [notification, ...prev]);\n      setUnreadCount(prev => prev + 1);\n\n      // Sadece basit bildirim - toast'a gerek yok aslında\n      // toast.success('Yeni bildiriminiz var');\n    };\n    socket.on('new_notification', handleNewNotification);\n    return () => {\n      socket.off('new_notification', handleNewNotification);\n    };\n  }, [socket]);\n  const loadNotifications = async () => {\n    try {\n      const response = await api.get('/notifications');\n      setNotifications(response.data.notifications || []);\n    } catch (error) {\n      console.error('Bildirimler yüklenirken hata:', error);\n      toast.error('Bildirimler yüklenemedi');\n    }\n  };\n  const loadUnreadCount = async () => {\n    try {\n      const response = await api.get('/notifications/unread-count');\n      setUnreadCount(response.data.count || 0);\n    } catch (error) {\n      console.error('Okunmamış bildirim sayısı yüklenirken hata:', error);\n    }\n  };\n\n  // Bildirim tipine göre ikon\n  const getNotificationIcon = type => {\n    switch (type) {\n      case 'message':\n        return /*#__PURE__*/_jsxDEV(ChatBubbleLeftRightIcon, {\n          className: \"h-5 w-5 text-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 16\n        }, this);\n      case 'appointment':\n        return /*#__PURE__*/_jsxDEV(CalendarIcon, {\n          className: \"h-5 w-5 text-green-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 16\n        }, this);\n      case 'session':\n        return /*#__PURE__*/_jsxDEV(ClockIcon, {\n          className: \"h-5 w-5 text-orange-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 16\n        }, this);\n      case 'system':\n        return /*#__PURE__*/_jsxDEV(ExclamationTriangleIcon, {\n          className: \"h-5 w-5 text-gray-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(BellIcon, {\n          className: \"h-5 w-5 text-gray-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // Bildirimi okundu olarak işaretle\n  const markAsRead = async notificationId => {\n    try {\n      await api.put(`/notifications/${notificationId}/read`);\n      setNotifications(prev => prev.map(notification => notification.id === notificationId ? {\n        ...notification,\n        isRead: true\n      } : notification));\n      setUnreadCount(prev => Math.max(0, prev - 1));\n    } catch (error) {\n      console.error('Bildirim okundu işaretlenirken hata:', error);\n      toast.error('Bildirim güncellenemedi');\n    }\n  };\n\n  // Tüm bildirimleri okundu olarak işaretle\n  const markAllAsRead = async () => {\n    try {\n      await api.put('/notifications/mark-all-read');\n      setNotifications(prev => prev.map(notification => ({\n        ...notification,\n        isRead: true\n      })));\n      setUnreadCount(0);\n      setIsOpen(false); // Dropdown'ı kapat\n      toast.success('Tüm bildirimler okundu işaretlendi');\n    } catch (error) {\n      console.error('Bildirimler okundu işaretlenirken hata:', error);\n      toast.error('Bildirimler güncellenemedi');\n    }\n  };\n\n  // Bildirimi sil\n  const deleteNotification = async notificationId => {\n    try {\n      await api.delete(`/notifications/${notificationId}`);\n      const notification = notifications.find(n => n.id === notificationId);\n      setNotifications(prev => prev.filter(n => n.id !== notificationId));\n      if (notification && !notification.isRead) {\n        setUnreadCount(prev => Math.max(0, prev - 1));\n      }\n      toast.success('Bildirim silindi');\n    } catch (error) {\n      console.error('Bildirim silinirken hata:', error);\n      toast.error('Bildirim silinemedi');\n    }\n  };\n\n  // Dropdown pozisyonunu hesapla\n  const getDropdownPosition = () => {\n    if (!buttonRef) return {\n      top: 0,\n      left: 0\n    };\n    const rect = buttonRef.getBoundingClientRect();\n    return {\n      top: rect.bottom + 8,\n      left: Math.max(16, rect.left - 160) // 160px dropdown'ın yarısı, minimum 16px margin\n    };\n  };\n\n  // Bildirime tıklama - ilgili sayfaya yönlendir\n  const handleNotificationClick = async notification => {\n    // Önce okundu işaretle\n    if (!notification.isRead) {\n      await markAsRead(notification.id);\n    }\n\n    // Dropdown'ı kapat\n    setIsOpen(false);\n\n    // Bildirim tipine göre yönlendir\n    if (notification.type === 'message') {\n      var _user$role, _user$role2;\n      // Kullanıcı rolüne göre mesaj sayfasına yönlendir ve conversation ID'yi state olarak geç\n      if ((user === null || user === void 0 ? void 0 : (_user$role = user.role) === null || _user$role === void 0 ? void 0 : _user$role.name) === 'Client') {\n        navigate('/client/messages', {\n          state: {\n            selectedConversationId: notification.relatedEntityId\n          }\n        });\n      } else if ((user === null || user === void 0 ? void 0 : (_user$role2 = user.role) === null || _user$role2 === void 0 ? void 0 : _user$role2.name) === 'Expert') {\n        navigate('/expert/messages', {\n          state: {\n            selectedConversationId: notification.relatedEntityId\n          }\n        });\n      }\n    } else if (notification.type === 'appointment') {\n      var _user$role3, _user$role4;\n      // Randevu sayfasına yönlendir\n      if ((user === null || user === void 0 ? void 0 : (_user$role3 = user.role) === null || _user$role3 === void 0 ? void 0 : _user$role3.name) === 'Client') {\n        navigate('/client/appointments');\n      } else if ((user === null || user === void 0 ? void 0 : (_user$role4 = user.role) === null || _user$role4 === void 0 ? void 0 : _user$role4.name) === 'Expert') {\n        navigate('/expert/appointments');\n      }\n    } else if (notification.type === 'session') {\n      var _user$role5, _user$role6;\n      // Seans sayfasına yönlendir\n      if ((user === null || user === void 0 ? void 0 : (_user$role5 = user.role) === null || _user$role5 === void 0 ? void 0 : _user$role5.name) === 'Client') {\n        navigate('/client/sessions');\n      } else if ((user === null || user === void 0 ? void 0 : (_user$role6 = user.role) === null || _user$role6 === void 0 ? void 0 : _user$role6.name) === 'Expert') {\n        navigate('/expert/sessions');\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"relative\",\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      ref: setButtonRef,\n      onClick: () => setIsOpen(!isOpen),\n      className: \"relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-md\",\n      children: [unreadCount > 0 ? /*#__PURE__*/_jsxDEV(BellSolidIcon, {\n        className: \"h-6 w-6 text-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(BellIcon, {\n        className: \"h-6 w-6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 11\n      }, this), unreadCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"absolute -top-0.5 -right-0.5 inline-flex items-center justify-center px-1.5 py-0.5 text-xs font-bold leading-none text-white bg-red-500 rounded-full min-w-[18px] h-[18px]\",\n        children: unreadCount > 99 ? '99+' : unreadCount\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 7\n    }, this), isOpen && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 z-[9998]\",\n        onClick: () => setIsOpen(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed w-80 bg-white rounded-lg shadow-xl ring-1 ring-black ring-opacity-5 z-[9999] max-h-96 overflow-hidden\",\n        style: {\n          top: `${getDropdownPosition().top}px`,\n          left: `${getDropdownPosition().left}px`,\n          zIndex: 9999\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4 py-3 border-b border-gray-200 flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-sm font-medium text-gray-900\",\n            children: \"Bildirimler\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 15\n          }, this), unreadCount > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: markAllAsRead,\n            className: \"text-xs text-primary-600 hover:text-primary-700 font-medium\",\n            children: \"T\\xFCm\\xFCn\\xFC Okundu \\u0130\\u015Faretle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-h-80 overflow-y-auto\",\n          children: notifications.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"px-4 py-8 text-center\",\n            children: [/*#__PURE__*/_jsxDEV(BellIcon, {\n              className: \"mx-auto h-12 w-12 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-2 text-sm text-gray-500\",\n              children: \"Hen\\xFCz bildiriminiz yok\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 17\n          }, this) : notifications.map(notification => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `px-4 py-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer ${!notification.isRead ? 'bg-blue-50' : ''}`,\n            onClick: () => handleNotificationClick(notification),\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: notification.avatar ? /*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"h-8 w-8 rounded-full\",\n                  src: notification.avatar,\n                  alt: \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 27\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center\",\n                  children: getNotificationIcon(notification.type)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1 min-w-0\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: notification.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 340,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600 mt-1\",\n                      children: notification.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 343,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500 mt-1\",\n                      children: (() => {\n                        try {\n                          const date = new Date(notification.timestamp);\n                          if (isNaN(date.getTime())) {\n                            return 'Geçersiz tarih';\n                          }\n                          return formatDistanceToNow(date, {\n                            addSuffix: true,\n                            locale: tr\n                          });\n                        } catch (error) {\n                          console.error('Date formatting error:', error, notification.timestamp);\n                          return 'Tarih hatası';\n                        }\n                      })()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 346,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-1 ml-2\",\n                    children: [!notification.isRead && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 368,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: e => {\n                        e.stopPropagation();\n                        deleteNotification(notification.id);\n                      },\n                      className: \"p-1 text-gray-400 hover:text-gray-600 rounded\",\n                      children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 377,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 370,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 366,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 21\n            }, this)\n          }, notification.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 13\n        }, this), notifications.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4 py-3 border-t border-gray-200 bg-gray-50\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsOpen(false),\n            className: \"w-full text-center text-sm text-primary-600 hover:text-primary-700 font-medium\",\n            children: \"T\\xFCm Bildirimleri G\\xF6r\\xFCnt\\xFCle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 252,\n    columnNumber: 5\n  }, this);\n};\n_s(NotificationCenter, \"SIXuDnzVT11YJp82leYcLdibi80=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = NotificationCenter;\nexport default NotificationCenter;\nvar _c;\n$RefreshReg$(_c, \"NotificationCenter\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "BellIcon", "XMarkIcon", "CheckIcon", "ClockIcon", "ChatBubbleLeftRightIcon", "CalendarIcon", "UserIcon", "ExclamationTriangleIcon", "BellSolidIcon", "format", "formatDistanceToNow", "tr", "api", "toast", "io", "useAuth", "useNavigate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "NotificationCenter", "_s", "isOpen", "setIsOpen", "notifications", "setNotifications", "unreadCount", "setUnreadCount", "buttonRef", "setButtonRef", "socket", "setSocket", "user", "navigate", "loadNotifications", "loadUnreadCount", "isMounted", "token", "localStorage", "getItem", "id", "connected", "console", "log", "socketConnection", "process", "env", "REACT_APP_API_URL", "auth", "transports", "reconnection", "reconnectionDelay", "reconnectionAttempts", "timeout", "forceNew", "upgrade", "rememberUpgrade", "on", "reason", "off", "disconnect", "hasToken", "<PERSON><PERSON>ser", "socketConnected", "handleNewNotification", "notification", "prev", "response", "get", "data", "error", "count", "getNotificationIcon", "type", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mark<PERSON><PERSON><PERSON>", "notificationId", "put", "map", "isRead", "Math", "max", "markAllAsRead", "success", "deleteNotification", "delete", "find", "n", "filter", "getDropdownPosition", "top", "left", "rect", "getBoundingClientRect", "bottom", "handleNotificationClick", "_user$role", "_user$role2", "role", "name", "state", "selectedConversationId", "relatedEntityId", "_user$role3", "_user$role4", "_user$role5", "_user$role6", "children", "ref", "onClick", "style", "zIndex", "length", "avatar", "src", "alt", "title", "message", "date", "Date", "timestamp", "isNaN", "getTime", "addSuffix", "locale", "e", "stopPropagation", "_c", "$RefreshReg$"], "sources": ["C:/Projeler/kidgarden/burky_root_web/client/src/components/notifications/NotificationCenter.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  BellIcon,\n  XMarkIcon,\n  CheckIcon,\n  ClockIcon,\n  ChatBubbleLeftRightIcon,\n  CalendarIcon,\n  UserIcon,\n  ExclamationTriangleIcon\n} from '@heroicons/react/24/outline';\nimport { BellIcon as BellSolidIcon } from '@heroicons/react/24/solid';\nimport { format, formatDistanceToNow } from 'date-fns';\nimport { tr } from 'date-fns/locale';\nimport api from '../../services/api';\nimport toast from 'react-hot-toast';\nimport io from 'socket.io-client';\nimport { useAuth } from '../../hooks/useAuth';\nimport { useNavigate } from 'react-router-dom';\n\n/**\n * Bildirim Merkezi Komponenti\n */\nconst NotificationCenter = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [notifications, setNotifications] = useState([]);\n  const [unreadCount, setUnreadCount] = useState(0);\n  const [buttonRef, setButtonRef] = useState(null);\n  const [socket, setSocket] = useState(null);\n  const { user } = useAuth();\n  const navigate = useNavigate();\n\n  // Load notifications from API\n  useEffect(() => {\n    loadNotifications();\n    loadUnreadCount();\n  }, []);\n\n  // Socket connection\n  useEffect(() => {\n    let isMounted = true;\n\n    const token = localStorage.getItem('accessToken');\n    if (token && user?.id && !socket?.connected) {\n      console.log('🔌 NOTIFICATIONS: Socket.IO bağlantısı kuruluyor... User ID:', user.id);\n      const socketConnection = io(process.env.REACT_APP_API_URL || 'https://192.168.1.111:5000', {\n        auth: { token },\n        transports: ['polling', 'websocket'], // Polling'i önce dene\n        reconnection: true,\n        reconnectionDelay: 2000,\n        reconnectionAttempts: 3,\n        timeout: 15000,\n        forceNew: false,\n        upgrade: false, // WebSocket upgrade'i devre dışı bırak\n        rememberUpgrade: false\n      });\n\n      setSocket(socketConnection);\n\n      // Connection events\n      socketConnection.on('connect', () => {\n        console.log('✅ NOTIFICATIONS: Socket.IO bağlantısı kuruldu:', socketConnection.id);\n      });\n\n      socketConnection.on('disconnect', (reason) => {\n        console.log('❌ NOTIFICATIONS: Socket.IO bağlantısı kesildi:', reason);\n      });\n\n      return () => {\n        isMounted = false;\n\n        // Güvenli disconnect\n        if (socketConnection?.connected) {\n          socketConnection.off(); // Tüm event listener'ları kaldır\n          socketConnection.disconnect();\n        }\n      };\n    } else {\n      console.log('❌ NOTIFICATIONS: Socket bağlantısı kurulamıyor - token veya user eksik:', {\n        hasToken: !!token,\n        hasUser: !!user?.id,\n        socketConnected: !!socket?.connected\n      });\n    }\n\n    return () => {\n      isMounted = false;\n    };\n  }, [user?.id]);\n\n  // Socket listener for new notifications\n  useEffect(() => {\n    if (!socket) return;\n\n    const handleNewNotification = (notification) => {\n      console.log('🔔 New notification received:', notification);\n\n      // Add new notification to the list\n      setNotifications(prev => [notification, ...prev]);\n      setUnreadCount(prev => prev + 1);\n\n      // Sadece basit bildirim - toast'a gerek yok aslında\n      // toast.success('Yeni bildiriminiz var');\n    };\n\n    socket.on('new_notification', handleNewNotification);\n\n    return () => {\n      socket.off('new_notification', handleNewNotification);\n    };\n  }, [socket]);\n\n  const loadNotifications = async () => {\n    try {\n      const response = await api.get('/notifications');\n      setNotifications(response.data.notifications || []);\n    } catch (error) {\n      console.error('Bildirimler yüklenirken hata:', error);\n      toast.error('Bildirimler yüklenemedi');\n    }\n  };\n\n  const loadUnreadCount = async () => {\n    try {\n      const response = await api.get('/notifications/unread-count');\n      setUnreadCount(response.data.count || 0);\n    } catch (error) {\n      console.error('Okunmamış bildirim sayısı yüklenirken hata:', error);\n    }\n  };\n\n  // Bildirim tipine göre ikon\n  const getNotificationIcon = (type) => {\n    switch (type) {\n      case 'message':\n        return <ChatBubbleLeftRightIcon className=\"h-5 w-5 text-blue-500\" />;\n      case 'appointment':\n        return <CalendarIcon className=\"h-5 w-5 text-green-500\" />;\n      case 'session':\n        return <ClockIcon className=\"h-5 w-5 text-orange-500\" />;\n      case 'system':\n        return <ExclamationTriangleIcon className=\"h-5 w-5 text-gray-500\" />;\n      default:\n        return <BellIcon className=\"h-5 w-5 text-gray-500\" />;\n    }\n  };\n\n  // Bildirimi okundu olarak işaretle\n  const markAsRead = async (notificationId) => {\n    try {\n      await api.put(`/notifications/${notificationId}/read`);\n\n      setNotifications(prev =>\n        prev.map(notification =>\n          notification.id === notificationId\n            ? { ...notification, isRead: true }\n            : notification\n        )\n      );\n      setUnreadCount(prev => Math.max(0, prev - 1));\n    } catch (error) {\n      console.error('Bildirim okundu işaretlenirken hata:', error);\n      toast.error('Bildirim güncellenemedi');\n    }\n  };\n\n  // Tüm bildirimleri okundu olarak işaretle\n  const markAllAsRead = async () => {\n    try {\n      await api.put('/notifications/mark-all-read');\n\n      setNotifications(prev =>\n        prev.map(notification => ({ ...notification, isRead: true }))\n      );\n      setUnreadCount(0);\n      setIsOpen(false); // Dropdown'ı kapat\n      toast.success('Tüm bildirimler okundu işaretlendi');\n    } catch (error) {\n      console.error('Bildirimler okundu işaretlenirken hata:', error);\n      toast.error('Bildirimler güncellenemedi');\n    }\n  };\n\n  // Bildirimi sil\n  const deleteNotification = async (notificationId) => {\n    try {\n      await api.delete(`/notifications/${notificationId}`);\n\n      const notification = notifications.find(n => n.id === notificationId);\n      setNotifications(prev => prev.filter(n => n.id !== notificationId));\n      if (notification && !notification.isRead) {\n        setUnreadCount(prev => Math.max(0, prev - 1));\n      }\n      toast.success('Bildirim silindi');\n    } catch (error) {\n      console.error('Bildirim silinirken hata:', error);\n      toast.error('Bildirim silinemedi');\n    }\n  };\n\n  // Dropdown pozisyonunu hesapla\n  const getDropdownPosition = () => {\n    if (!buttonRef) return { top: 0, left: 0 };\n\n    const rect = buttonRef.getBoundingClientRect();\n    return {\n      top: rect.bottom + 8,\n      left: Math.max(16, rect.left - 160) // 160px dropdown'ın yarısı, minimum 16px margin\n    };\n  };\n\n  // Bildirime tıklama - ilgili sayfaya yönlendir\n  const handleNotificationClick = async (notification) => {\n    // Önce okundu işaretle\n    if (!notification.isRead) {\n      await markAsRead(notification.id);\n    }\n\n    // Dropdown'ı kapat\n    setIsOpen(false);\n\n    // Bildirim tipine göre yönlendir\n    if (notification.type === 'message') {\n      // Kullanıcı rolüne göre mesaj sayfasına yönlendir ve conversation ID'yi state olarak geç\n      if (user?.role?.name === 'Client') {\n        navigate('/client/messages', {\n          state: { selectedConversationId: notification.relatedEntityId }\n        });\n      } else if (user?.role?.name === 'Expert') {\n        navigate('/expert/messages', {\n          state: { selectedConversationId: notification.relatedEntityId }\n        });\n      }\n    } else if (notification.type === 'appointment') {\n      // Randevu sayfasına yönlendir\n      if (user?.role?.name === 'Client') {\n        navigate('/client/appointments');\n      } else if (user?.role?.name === 'Expert') {\n        navigate('/expert/appointments');\n      }\n    } else if (notification.type === 'session') {\n      // Seans sayfasına yönlendir\n      if (user?.role?.name === 'Client') {\n        navigate('/client/sessions');\n      } else if (user?.role?.name === 'Expert') {\n        navigate('/expert/sessions');\n      }\n    }\n  };\n\n  return (\n    <div className=\"relative\">\n      {/* Bildirim Butonu */}\n      <button\n        ref={setButtonRef}\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-md\"\n      >\n        {unreadCount > 0 ? (\n          <BellSolidIcon className=\"h-6 w-6 text-primary-600\" />\n        ) : (\n          <BellIcon className=\"h-6 w-6\" />\n        )}\n        \n        {/* Okunmamış bildirim sayısı */}\n        {unreadCount > 0 && (\n          <span className=\"absolute -top-0.5 -right-0.5 inline-flex items-center justify-center px-1.5 py-0.5 text-xs font-bold leading-none text-white bg-red-500 rounded-full min-w-[18px] h-[18px]\">\n            {unreadCount > 99 ? '99+' : unreadCount}\n          </span>\n        )}\n      </button>\n\n      {/* Bildirim Paneli */}\n      {isOpen && (\n        <>\n          {/* Overlay */}\n          <div\n            className=\"fixed inset-0 z-[9998]\"\n            onClick={() => setIsOpen(false)}\n          />\n          \n          {/* Bildirim Dropdown */}\n          <div\n            className=\"fixed w-80 bg-white rounded-lg shadow-xl ring-1 ring-black ring-opacity-5 z-[9999] max-h-96 overflow-hidden\"\n            style={{\n              top: `${getDropdownPosition().top}px`,\n              left: `${getDropdownPosition().left}px`,\n              zIndex: 9999\n            }}\n          >\n            {/* Header */}\n            <div className=\"px-4 py-3 border-b border-gray-200 flex items-center justify-between\">\n              <h3 className=\"text-sm font-medium text-gray-900\">Bildirimler</h3>\n              {unreadCount > 0 && (\n                <button\n                  onClick={markAllAsRead}\n                  className=\"text-xs text-primary-600 hover:text-primary-700 font-medium\"\n                >\n                  Tümünü Okundu İşaretle\n                </button>\n              )}\n            </div>\n\n            {/* Bildirim Listesi */}\n            <div className=\"max-h-80 overflow-y-auto\">\n              {notifications.length === 0 ? (\n                <div className=\"px-4 py-8 text-center\">\n                  <BellIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                  <p className=\"mt-2 text-sm text-gray-500\">Henüz bildiriminiz yok</p>\n                </div>\n              ) : (\n                notifications.map((notification) => (\n                  <div\n                    key={notification.id}\n                    className={`px-4 py-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer ${\n                      !notification.isRead ? 'bg-blue-50' : ''\n                    }`}\n                    onClick={() => handleNotificationClick(notification)}\n                  >\n                    <div className=\"flex items-start space-x-3\">\n                      {/* Avatar veya İkon */}\n                      <div className=\"flex-shrink-0\">\n                        {notification.avatar ? (\n                          <img\n                            className=\"h-8 w-8 rounded-full\"\n                            src={notification.avatar}\n                            alt=\"\"\n                          />\n                        ) : (\n                          <div className=\"h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center\">\n                            {getNotificationIcon(notification.type)}\n                          </div>\n                        )}\n                      </div>\n\n                      {/* İçerik */}\n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex items-start justify-between\">\n                          <div className=\"flex-1\">\n                            <p className=\"text-sm font-medium text-gray-900\">\n                              {notification.title}\n                            </p>\n                            <p className=\"text-sm text-gray-600 mt-1\">\n                              {notification.message}\n                            </p>\n                            <p className=\"text-xs text-gray-500 mt-1\">\n                              {(() => {\n                                try {\n                                  const date = new Date(notification.timestamp);\n                                  if (isNaN(date.getTime())) {\n                                    return 'Geçersiz tarih';\n                                  }\n                                  return formatDistanceToNow(date, {\n                                    addSuffix: true,\n                                    locale: tr\n                                  });\n                                } catch (error) {\n                                  console.error('Date formatting error:', error, notification.timestamp);\n                                  return 'Tarih hatası';\n                                }\n                              })()}\n                            </p>\n                          </div>\n\n                          {/* Okunmamış işareti ve sil butonu */}\n                          <div className=\"flex items-center space-x-1 ml-2\">\n                            {!notification.isRead && (\n                              <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                            )}\n                            <button\n                              onClick={(e) => {\n                                e.stopPropagation();\n                                deleteNotification(notification.id);\n                              }}\n                              className=\"p-1 text-gray-400 hover:text-gray-600 rounded\"\n                            >\n                              <XMarkIcon className=\"h-4 w-4\" />\n                            </button>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n\n            {/* Footer */}\n            {notifications.length > 0 && (\n              <div className=\"px-4 py-3 border-t border-gray-200 bg-gray-50\">\n                <button\n                  onClick={() => setIsOpen(false)}\n                  className=\"w-full text-center text-sm text-primary-600 hover:text-primary-700 font-medium\"\n                >\n                  Tüm Bildirimleri Görüntüle\n                </button>\n              </div>\n            )}\n          </div>\n        </>\n      )}\n    </div>\n  );\n};\n\nexport default NotificationCenter;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,QAAQ,EACRC,SAAS,EACTC,SAAS,EACTC,SAAS,EACTC,uBAAuB,EACvBC,YAAY,EACZC,QAAQ,EACRC,uBAAuB,QAClB,6BAA6B;AACpC,SAASP,QAAQ,IAAIQ,aAAa,QAAQ,2BAA2B;AACrE,SAASC,MAAM,EAAEC,mBAAmB,QAAQ,UAAU;AACtD,SAASC,EAAE,QAAQ,iBAAiB;AACpC,OAAOC,GAAG,MAAM,oBAAoB;AACpC,OAAOC,KAAK,MAAM,iBAAiB;AACnC,OAAOC,EAAE,MAAM,kBAAkB;AACjC,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,WAAW,QAAQ,kBAAkB;;AAE9C;AACA;AACA;AAFA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACiC,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM;IAAEmC;EAAK,CAAC,GAAGlB,OAAO,CAAC,CAAC;EAC1B,MAAMmB,QAAQ,GAAGlB,WAAW,CAAC,CAAC;;EAE9B;EACAjB,SAAS,CAAC,MAAM;IACdoC,iBAAiB,CAAC,CAAC;IACnBC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACArC,SAAS,CAAC,MAAM;IACd,IAAIsC,SAAS,GAAG,IAAI;IAEpB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACjD,IAAIF,KAAK,IAAIL,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEQ,EAAE,IAAI,EAACV,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEW,SAAS,GAAE;MAC3CC,OAAO,CAACC,GAAG,CAAC,8DAA8D,EAAEX,IAAI,CAACQ,EAAE,CAAC;MACpF,MAAMI,gBAAgB,GAAG/B,EAAE,CAACgC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,4BAA4B,EAAE;QACzFC,IAAI,EAAE;UAAEX;QAAM,CAAC;QACfY,UAAU,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC;QAAE;QACtCC,YAAY,EAAE,IAAI;QAClBC,iBAAiB,EAAE,IAAI;QACvBC,oBAAoB,EAAE,CAAC;QACvBC,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE,KAAK;QACfC,OAAO,EAAE,KAAK;QAAE;QAChBC,eAAe,EAAE;MACnB,CAAC,CAAC;MAEFzB,SAAS,CAACa,gBAAgB,CAAC;;MAE3B;MACAA,gBAAgB,CAACa,EAAE,CAAC,SAAS,EAAE,MAAM;QACnCf,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEC,gBAAgB,CAACJ,EAAE,CAAC;MACpF,CAAC,CAAC;MAEFI,gBAAgB,CAACa,EAAE,CAAC,YAAY,EAAGC,MAAM,IAAK;QAC5ChB,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEe,MAAM,CAAC;MACvE,CAAC,CAAC;MAEF,OAAO,MAAM;QACXtB,SAAS,GAAG,KAAK;;QAEjB;QACA,IAAIQ,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEH,SAAS,EAAE;UAC/BG,gBAAgB,CAACe,GAAG,CAAC,CAAC,CAAC,CAAC;UACxBf,gBAAgB,CAACgB,UAAU,CAAC,CAAC;QAC/B;MACF,CAAC;IACH,CAAC,MAAM;MACLlB,OAAO,CAACC,GAAG,CAAC,yEAAyE,EAAE;QACrFkB,QAAQ,EAAE,CAAC,CAACxB,KAAK;QACjByB,OAAO,EAAE,CAAC,EAAC9B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEQ,EAAE;QACnBuB,eAAe,EAAE,CAAC,EAACjC,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEW,SAAS;MACtC,CAAC,CAAC;IACJ;IAEA,OAAO,MAAM;MACXL,SAAS,GAAG,KAAK;IACnB,CAAC;EACH,CAAC,EAAE,CAACJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,EAAE,CAAC,CAAC;;EAEd;EACA1C,SAAS,CAAC,MAAM;IACd,IAAI,CAACgC,MAAM,EAAE;IAEb,MAAMkC,qBAAqB,GAAIC,YAAY,IAAK;MAC9CvB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEsB,YAAY,CAAC;;MAE1D;MACAxC,gBAAgB,CAACyC,IAAI,IAAI,CAACD,YAAY,EAAE,GAAGC,IAAI,CAAC,CAAC;MACjDvC,cAAc,CAACuC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;;MAEhC;MACA;IACF,CAAC;IAEDpC,MAAM,CAAC2B,EAAE,CAAC,kBAAkB,EAAEO,qBAAqB,CAAC;IAEpD,OAAO,MAAM;MACXlC,MAAM,CAAC6B,GAAG,CAAC,kBAAkB,EAAEK,qBAAqB,CAAC;IACvD,CAAC;EACH,CAAC,EAAE,CAAClC,MAAM,CAAC,CAAC;EAEZ,MAAMI,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMiC,QAAQ,GAAG,MAAMxD,GAAG,CAACyD,GAAG,CAAC,gBAAgB,CAAC;MAChD3C,gBAAgB,CAAC0C,QAAQ,CAACE,IAAI,CAAC7C,aAAa,IAAI,EAAE,CAAC;IACrD,CAAC,CAAC,OAAO8C,KAAK,EAAE;MACd5B,OAAO,CAAC4B,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD1D,KAAK,CAAC0D,KAAK,CAAC,yBAAyB,CAAC;IACxC;EACF,CAAC;EAED,MAAMnC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMgC,QAAQ,GAAG,MAAMxD,GAAG,CAACyD,GAAG,CAAC,6BAA6B,CAAC;MAC7DzC,cAAc,CAACwC,QAAQ,CAACE,IAAI,CAACE,KAAK,IAAI,CAAC,CAAC;IAC1C,CAAC,CAAC,OAAOD,KAAK,EAAE;MACd5B,OAAO,CAAC4B,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;IACrE;EACF,CAAC;;EAED;EACA,MAAME,mBAAmB,GAAIC,IAAI,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,SAAS;QACZ,oBAAOxD,OAAA,CAACd,uBAAuB;UAACuE,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtE,KAAK,aAAa;QAChB,oBAAO7D,OAAA,CAACb,YAAY;UAACsE,SAAS,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5D,KAAK,SAAS;QACZ,oBAAO7D,OAAA,CAACf,SAAS;UAACwE,SAAS,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC1D,KAAK,QAAQ;QACX,oBAAO7D,OAAA,CAACX,uBAAuB;UAACoE,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtE;QACE,oBAAO7D,OAAA,CAAClB,QAAQ;UAAC2E,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACzD;EACF,CAAC;;EAED;EACA,MAAMC,UAAU,GAAG,MAAOC,cAAc,IAAK;IAC3C,IAAI;MACF,MAAMrE,GAAG,CAACsE,GAAG,CAAC,kBAAkBD,cAAc,OAAO,CAAC;MAEtDvD,gBAAgB,CAACyC,IAAI,IACnBA,IAAI,CAACgB,GAAG,CAACjB,YAAY,IACnBA,YAAY,CAACzB,EAAE,KAAKwC,cAAc,GAC9B;QAAE,GAAGf,YAAY;QAAEkB,MAAM,EAAE;MAAK,CAAC,GACjClB,YACN,CACF,CAAC;MACDtC,cAAc,CAACuC,IAAI,IAAIkB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEnB,IAAI,GAAG,CAAC,CAAC,CAAC;IAC/C,CAAC,CAAC,OAAOI,KAAK,EAAE;MACd5B,OAAO,CAAC4B,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D1D,KAAK,CAAC0D,KAAK,CAAC,yBAAyB,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAMgB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAM3E,GAAG,CAACsE,GAAG,CAAC,8BAA8B,CAAC;MAE7CxD,gBAAgB,CAACyC,IAAI,IACnBA,IAAI,CAACgB,GAAG,CAACjB,YAAY,KAAK;QAAE,GAAGA,YAAY;QAAEkB,MAAM,EAAE;MAAK,CAAC,CAAC,CAC9D,CAAC;MACDxD,cAAc,CAAC,CAAC,CAAC;MACjBJ,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;MAClBX,KAAK,CAAC2E,OAAO,CAAC,oCAAoC,CAAC;IACrD,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACd5B,OAAO,CAAC4B,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D1D,KAAK,CAAC0D,KAAK,CAAC,4BAA4B,CAAC;IAC3C;EACF,CAAC;;EAED;EACA,MAAMkB,kBAAkB,GAAG,MAAOR,cAAc,IAAK;IACnD,IAAI;MACF,MAAMrE,GAAG,CAAC8E,MAAM,CAAC,kBAAkBT,cAAc,EAAE,CAAC;MAEpD,MAAMf,YAAY,GAAGzC,aAAa,CAACkE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnD,EAAE,KAAKwC,cAAc,CAAC;MACrEvD,gBAAgB,CAACyC,IAAI,IAAIA,IAAI,CAAC0B,MAAM,CAACD,CAAC,IAAIA,CAAC,CAACnD,EAAE,KAAKwC,cAAc,CAAC,CAAC;MACnE,IAAIf,YAAY,IAAI,CAACA,YAAY,CAACkB,MAAM,EAAE;QACxCxD,cAAc,CAACuC,IAAI,IAAIkB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEnB,IAAI,GAAG,CAAC,CAAC,CAAC;MAC/C;MACAtD,KAAK,CAAC2E,OAAO,CAAC,kBAAkB,CAAC;IACnC,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACd5B,OAAO,CAAC4B,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD1D,KAAK,CAAC0D,KAAK,CAAC,qBAAqB,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAMuB,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAACjE,SAAS,EAAE,OAAO;MAAEkE,GAAG,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAC;IAE1C,MAAMC,IAAI,GAAGpE,SAAS,CAACqE,qBAAqB,CAAC,CAAC;IAC9C,OAAO;MACLH,GAAG,EAAEE,IAAI,CAACE,MAAM,GAAG,CAAC;MACpBH,IAAI,EAAEX,IAAI,CAACC,GAAG,CAAC,EAAE,EAAEW,IAAI,CAACD,IAAI,GAAG,GAAG,CAAC,CAAC;IACtC,CAAC;EACH,CAAC;;EAED;EACA,MAAMI,uBAAuB,GAAG,MAAOlC,YAAY,IAAK;IACtD;IACA,IAAI,CAACA,YAAY,CAACkB,MAAM,EAAE;MACxB,MAAMJ,UAAU,CAACd,YAAY,CAACzB,EAAE,CAAC;IACnC;;IAEA;IACAjB,SAAS,CAAC,KAAK,CAAC;;IAEhB;IACA,IAAI0C,YAAY,CAACQ,IAAI,KAAK,SAAS,EAAE;MAAA,IAAA2B,UAAA,EAAAC,WAAA;MACnC;MACA,IAAI,CAAArE,IAAI,aAAJA,IAAI,wBAAAoE,UAAA,GAAJpE,IAAI,CAAEsE,IAAI,cAAAF,UAAA,uBAAVA,UAAA,CAAYG,IAAI,MAAK,QAAQ,EAAE;QACjCtE,QAAQ,CAAC,kBAAkB,EAAE;UAC3BuE,KAAK,EAAE;YAAEC,sBAAsB,EAAExC,YAAY,CAACyC;UAAgB;QAChE,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI,CAAA1E,IAAI,aAAJA,IAAI,wBAAAqE,WAAA,GAAJrE,IAAI,CAAEsE,IAAI,cAAAD,WAAA,uBAAVA,WAAA,CAAYE,IAAI,MAAK,QAAQ,EAAE;QACxCtE,QAAQ,CAAC,kBAAkB,EAAE;UAC3BuE,KAAK,EAAE;YAAEC,sBAAsB,EAAExC,YAAY,CAACyC;UAAgB;QAChE,CAAC,CAAC;MACJ;IACF,CAAC,MAAM,IAAIzC,YAAY,CAACQ,IAAI,KAAK,aAAa,EAAE;MAAA,IAAAkC,WAAA,EAAAC,WAAA;MAC9C;MACA,IAAI,CAAA5E,IAAI,aAAJA,IAAI,wBAAA2E,WAAA,GAAJ3E,IAAI,CAAEsE,IAAI,cAAAK,WAAA,uBAAVA,WAAA,CAAYJ,IAAI,MAAK,QAAQ,EAAE;QACjCtE,QAAQ,CAAC,sBAAsB,CAAC;MAClC,CAAC,MAAM,IAAI,CAAAD,IAAI,aAAJA,IAAI,wBAAA4E,WAAA,GAAJ5E,IAAI,CAAEsE,IAAI,cAAAM,WAAA,uBAAVA,WAAA,CAAYL,IAAI,MAAK,QAAQ,EAAE;QACxCtE,QAAQ,CAAC,sBAAsB,CAAC;MAClC;IACF,CAAC,MAAM,IAAIgC,YAAY,CAACQ,IAAI,KAAK,SAAS,EAAE;MAAA,IAAAoC,WAAA,EAAAC,WAAA;MAC1C;MACA,IAAI,CAAA9E,IAAI,aAAJA,IAAI,wBAAA6E,WAAA,GAAJ7E,IAAI,CAAEsE,IAAI,cAAAO,WAAA,uBAAVA,WAAA,CAAYN,IAAI,MAAK,QAAQ,EAAE;QACjCtE,QAAQ,CAAC,kBAAkB,CAAC;MAC9B,CAAC,MAAM,IAAI,CAAAD,IAAI,aAAJA,IAAI,wBAAA8E,WAAA,GAAJ9E,IAAI,CAAEsE,IAAI,cAAAQ,WAAA,uBAAVA,WAAA,CAAYP,IAAI,MAAK,QAAQ,EAAE;QACxCtE,QAAQ,CAAC,kBAAkB,CAAC;MAC9B;IACF;EACF,CAAC;EAED,oBACEhB,OAAA;IAAKyD,SAAS,EAAC,UAAU;IAAAqC,QAAA,gBAEvB9F,OAAA;MACE+F,GAAG,EAAEnF,YAAa;MAClBoF,OAAO,EAAEA,CAAA,KAAM1F,SAAS,CAAC,CAACD,MAAM,CAAE;MAClCoD,SAAS,EAAC,sIAAsI;MAAAqC,QAAA,GAE/IrF,WAAW,GAAG,CAAC,gBACdT,OAAA,CAACV,aAAa;QAACmE,SAAS,EAAC;MAA0B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAEtD7D,OAAA,CAAClB,QAAQ;QAAC2E,SAAS,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAChC,EAGApD,WAAW,GAAG,CAAC,iBACdT,OAAA;QAAMyD,SAAS,EAAC,4KAA4K;QAAAqC,QAAA,EACzLrF,WAAW,GAAG,EAAE,GAAG,KAAK,GAAGA;MAAW;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,EAGRxD,MAAM,iBACLL,OAAA,CAAAE,SAAA;MAAA4F,QAAA,gBAEE9F,OAAA;QACEyD,SAAS,EAAC,wBAAwB;QAClCuC,OAAO,EAAEA,CAAA,KAAM1F,SAAS,CAAC,KAAK;MAAE;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eAGF7D,OAAA;QACEyD,SAAS,EAAC,6GAA6G;QACvHwC,KAAK,EAAE;UACLpB,GAAG,EAAE,GAAGD,mBAAmB,CAAC,CAAC,CAACC,GAAG,IAAI;UACrCC,IAAI,EAAE,GAAGF,mBAAmB,CAAC,CAAC,CAACE,IAAI,IAAI;UACvCoB,MAAM,EAAE;QACV,CAAE;QAAAJ,QAAA,gBAGF9F,OAAA;UAAKyD,SAAS,EAAC,sEAAsE;UAAAqC,QAAA,gBACnF9F,OAAA;YAAIyD,SAAS,EAAC,mCAAmC;YAAAqC,QAAA,EAAC;UAAW;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACjEpD,WAAW,GAAG,CAAC,iBACdT,OAAA;YACEgG,OAAO,EAAE3B,aAAc;YACvBZ,SAAS,EAAC,6DAA6D;YAAAqC,QAAA,EACxE;UAED;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN7D,OAAA;UAAKyD,SAAS,EAAC,0BAA0B;UAAAqC,QAAA,EACtCvF,aAAa,CAAC4F,MAAM,KAAK,CAAC,gBACzBnG,OAAA;YAAKyD,SAAS,EAAC,uBAAuB;YAAAqC,QAAA,gBACpC9F,OAAA,CAAClB,QAAQ;cAAC2E,SAAS,EAAC;YAAiC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxD7D,OAAA;cAAGyD,SAAS,EAAC,4BAA4B;cAAAqC,QAAA,EAAC;YAAsB;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,GAENtD,aAAa,CAAC0D,GAAG,CAAEjB,YAAY,iBAC7BhD,OAAA;YAEEyD,SAAS,EAAE,sEACT,CAACT,YAAY,CAACkB,MAAM,GAAG,YAAY,GAAG,EAAE,EACvC;YACH8B,OAAO,EAAEA,CAAA,KAAMd,uBAAuB,CAAClC,YAAY,CAAE;YAAA8C,QAAA,eAErD9F,OAAA;cAAKyD,SAAS,EAAC,4BAA4B;cAAAqC,QAAA,gBAEzC9F,OAAA;gBAAKyD,SAAS,EAAC,eAAe;gBAAAqC,QAAA,EAC3B9C,YAAY,CAACoD,MAAM,gBAClBpG,OAAA;kBACEyD,SAAS,EAAC,sBAAsB;kBAChC4C,GAAG,EAAErD,YAAY,CAACoD,MAAO;kBACzBE,GAAG,EAAC;gBAAE;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,gBAEF7D,OAAA;kBAAKyD,SAAS,EAAC,mEAAmE;kBAAAqC,QAAA,EAC/EvC,mBAAmB,CAACP,YAAY,CAACQ,IAAI;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGN7D,OAAA;gBAAKyD,SAAS,EAAC,gBAAgB;gBAAAqC,QAAA,eAC7B9F,OAAA;kBAAKyD,SAAS,EAAC,kCAAkC;kBAAAqC,QAAA,gBAC/C9F,OAAA;oBAAKyD,SAAS,EAAC,QAAQ;oBAAAqC,QAAA,gBACrB9F,OAAA;sBAAGyD,SAAS,EAAC,mCAAmC;sBAAAqC,QAAA,EAC7C9C,YAAY,CAACuD;oBAAK;sBAAA7C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB,CAAC,eACJ7D,OAAA;sBAAGyD,SAAS,EAAC,4BAA4B;sBAAAqC,QAAA,EACtC9C,YAAY,CAACwD;oBAAO;sBAAA9C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB,CAAC,eACJ7D,OAAA;sBAAGyD,SAAS,EAAC,4BAA4B;sBAAAqC,QAAA,EACtC,CAAC,MAAM;wBACN,IAAI;0BACF,MAAMW,IAAI,GAAG,IAAIC,IAAI,CAAC1D,YAAY,CAAC2D,SAAS,CAAC;0BAC7C,IAAIC,KAAK,CAACH,IAAI,CAACI,OAAO,CAAC,CAAC,CAAC,EAAE;4BACzB,OAAO,gBAAgB;0BACzB;0BACA,OAAOrH,mBAAmB,CAACiH,IAAI,EAAE;4BAC/BK,SAAS,EAAE,IAAI;4BACfC,MAAM,EAAEtH;0BACV,CAAC,CAAC;wBACJ,CAAC,CAAC,OAAO4D,KAAK,EAAE;0BACd5B,OAAO,CAAC4B,KAAK,CAAC,wBAAwB,EAAEA,KAAK,EAAEL,YAAY,CAAC2D,SAAS,CAAC;0BACtE,OAAO,cAAc;wBACvB;sBACF,CAAC,EAAE;oBAAC;sBAAAjD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eAGN7D,OAAA;oBAAKyD,SAAS,EAAC,kCAAkC;oBAAAqC,QAAA,GAC9C,CAAC9C,YAAY,CAACkB,MAAM,iBACnBlE,OAAA;sBAAKyD,SAAS,EAAC;oBAAkC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CACxD,eACD7D,OAAA;sBACEgG,OAAO,EAAGgB,CAAC,IAAK;wBACdA,CAAC,CAACC,eAAe,CAAC,CAAC;wBACnB1C,kBAAkB,CAACvB,YAAY,CAACzB,EAAE,CAAC;sBACrC,CAAE;sBACFkC,SAAS,EAAC,+CAA+C;sBAAAqC,QAAA,eAEzD9F,OAAA,CAACjB,SAAS;wBAAC0E,SAAS,EAAC;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GApEDb,YAAY,CAACzB,EAAE;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqEjB,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGLtD,aAAa,CAAC4F,MAAM,GAAG,CAAC,iBACvBnG,OAAA;UAAKyD,SAAS,EAAC,+CAA+C;UAAAqC,QAAA,eAC5D9F,OAAA;YACEgG,OAAO,EAAEA,CAAA,KAAM1F,SAAS,CAAC,KAAK,CAAE;YAChCmD,SAAS,EAAC,gFAAgF;YAAAqC,QAAA,EAC3F;UAED;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA,eACN,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACzD,EAAA,CA5XID,kBAAkB;EAAA,QAMLN,OAAO,EACPC,WAAW;AAAA;AAAAoH,EAAA,GAPxB/G,kBAAkB;AA8XxB,eAAeA,kBAAkB;AAAC,IAAA+G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}