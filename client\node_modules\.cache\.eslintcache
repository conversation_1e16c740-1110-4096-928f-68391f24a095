[{"C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\index.jsx": "1", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\App.jsx": "2", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\contexts\\AuthContext.jsx": "3", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\hooks\\useAuth.js": "4", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\PagePermission.jsx": "5", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ProtectedRoute.jsx": "6", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\PageTransition.jsx": "7", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\layout\\MainLayout.jsx": "8", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\layout\\AuthLayout.jsx": "9", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\services\\api.js": "10", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\auth\\LoginPage.jsx": "11", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\auth\\RegisterPage.jsx": "12", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\auth\\ForgotPasswordPage.jsx": "13", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\dashboard\\DashboardPage.jsx": "14", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\profile\\ProfilePage.jsx": "15", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\dashboard\\ExpertDashboardPage.jsx": "16", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\profile\\ExpertProfilePage.jsx": "17", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\users\\UsersPage.jsx": "18", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\profile\\ClientProfilePage.jsx": "19", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\users\\UserFormPage.jsx": "20", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\RolesPage.jsx": "21", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\RoleFormPage.jsx": "22", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\permissions\\PermissionsPage.jsx": "23", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\RolePermissionsPage.jsx": "24", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\audit-logs\\AuditLogsPage.jsx": "25", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\settings\\SystemSettingsPage.jsx": "26", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\profile\\AdminProfilePage.jsx": "27", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\availabilities\\index.js": "28", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\sessions\\index.js": "29", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\appointments\\index.js": "30", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\messages\\index.js": "31", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\reports\\index.js": "32", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\clients\\index.js": "33", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\dashboard\\index.js": "34", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\experts\\index.js": "35", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\appointments\\index.js": "36", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\messages\\index.js": "37", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\sessions\\index.js": "38", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\packages\\index.js": "39", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\payments\\index.js": "40", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\availabilities\\AvailabilityPage.jsx": "41", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\sessions\\SessionsPage.jsx": "42", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\appointments\\AppointmentsPage.jsx": "43", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\messages\\MessagesPage.jsx": "44", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\reports\\ReportsPage.jsx": "45", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\clients\\ClientsPage.jsx": "46", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\components\\PermissionMatrix.jsx": "47", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\appointments\\ClientAppointmentsPage.jsx": "48", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\dashboard\\ClientDashboardPage.jsx": "49", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\experts\\ClientExpertsPage.jsx": "50", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\messages\\ClientMessagesPage.jsx": "51", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\index.js": "52", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\packages\\ClientPackagesPage.jsx": "53", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\sessions\\ClientSessionsPage.jsx": "54", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\payments\\ClientPaymentsPage.jsx": "55", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormTextarea.jsx": "56", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormSelect.jsx": "57", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormRadio.jsx": "58", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormCheckbox.jsx": "59", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormInput.jsx": "60", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\common\\MultiSelect.jsx": "61", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\experts\\ClientExpertDetailPage.jsx": "62", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\appointments\\BookAppointmentPage.jsx": "63", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\notifications\\NotificationCenter.jsx": "64", "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\meeting\\MeetingPage.jsx": "65"}, {"size": 1078, "mtime": 1755081909717, "results": "66", "hashOfConfig": "67"}, {"size": 13093, "mtime": 1755031329574, "results": "68", "hashOfConfig": "67"}, {"size": 12396, "mtime": 1755030101660, "results": "69", "hashOfConfig": "67"}, {"size": 283, "mtime": 1742651186300, "results": "70", "hashOfConfig": "67"}, {"size": 1594, "mtime": 1755031392192, "results": "71", "hashOfConfig": "67"}, {"size": 808, "mtime": 1742651192925, "results": "72", "hashOfConfig": "67"}, {"size": 813, "mtime": 1743121345478, "results": "73", "hashOfConfig": "67"}, {"size": 26360, "mtime": 1754914178556, "results": "74", "hashOfConfig": "67"}, {"size": 840, "mtime": 1742651199783, "results": "75", "hashOfConfig": "67"}, {"size": 4044, "mtime": 1754906950669, "results": "76", "hashOfConfig": "67"}, {"size": 5161, "mtime": 1743023176067, "results": "77", "hashOfConfig": "67"}, {"size": 13594, "mtime": 1743023175878, "results": "78", "hashOfConfig": "67"}, {"size": 3680, "mtime": 1742839996729, "results": "79", "hashOfConfig": "67"}, {"size": 6149, "mtime": 1742652514170, "results": "80", "hashOfConfig": "67"}, {"size": 10004, "mtime": 1742687855831, "results": "81", "hashOfConfig": "67"}, {"size": 25808, "mtime": 1754942504054, "results": "82", "hashOfConfig": "67"}, {"size": 34266, "mtime": 1754517675551, "results": "83", "hashOfConfig": "67"}, {"size": 7579, "mtime": 1742681038274, "results": "84", "hashOfConfig": "67"}, {"size": 18240, "mtime": 1743121346919, "results": "85", "hashOfConfig": "67"}, {"size": 13581, "mtime": 1742729246617, "results": "86", "hashOfConfig": "67"}, {"size": 9374, "mtime": 1742685959799, "results": "87", "hashOfConfig": "67"}, {"size": 21513, "mtime": 1743085705486, "results": "88", "hashOfConfig": "67"}, {"size": 5307, "mtime": 1742686008746, "results": "89", "hashOfConfig": "67"}, {"size": 915, "mtime": 1742668681019, "results": "90", "hashOfConfig": "67"}, {"size": 10152, "mtime": 1742686053807, "results": "91", "hashOfConfig": "67"}, {"size": 16081, "mtime": 1742653511611, "results": "92", "hashOfConfig": "67"}, {"size": 183, "mtime": 1754672350696, "results": "93", "hashOfConfig": "67"}, {"size": 87, "mtime": 1742911290814, "results": "94", "hashOfConfig": "67"}, {"size": 75, "mtime": 1742926306611, "results": "95", "hashOfConfig": "67"}, {"size": 87, "mtime": 1742924113827, "results": "96", "hashOfConfig": "67"}, {"size": 75, "mtime": 1742928519256, "results": "97", "hashOfConfig": "67"}, {"size": 72, "mtime": 1742930957065, "results": "98", "hashOfConfig": "67"}, {"size": 72, "mtime": 1742930563398, "results": "99", "hashOfConfig": "67"}, {"size": 96, "mtime": 1742942293117, "results": "100", "hashOfConfig": "67"}, {"size": 189, "mtime": 1754672075729, "results": "101", "hashOfConfig": "67"}, {"size": 195, "mtime": 1754673752486, "results": "102", "hashOfConfig": "67"}, {"size": 93, "mtime": 1742952326351, "results": "103", "hashOfConfig": "67"}, {"size": 93, "mtime": 1742951288236, "results": "104", "hashOfConfig": "67"}, {"size": 93, "mtime": 1743107128763, "results": "105", "hashOfConfig": "67"}, {"size": 91, "mtime": 1743107140295, "results": "106", "hashOfConfig": "67"}, {"size": 33144, "mtime": 1754676655256, "results": "107", "hashOfConfig": "67"}, {"size": 23062, "mtime": 1755031596123, "results": "108", "hashOfConfig": "67"}, {"size": 22820, "mtime": 1754942357182, "results": "109", "hashOfConfig": "67"}, {"size": 35391, "mtime": 1754858963157, "results": "110", "hashOfConfig": "67"}, {"size": 33648, "mtime": 1742995680878, "results": "111", "hashOfConfig": "67"}, {"size": 19462, "mtime": 1754920395097, "results": "112", "hashOfConfig": "67"}, {"size": 8841, "mtime": 1742669558119, "results": "113", "hashOfConfig": "67"}, {"size": 21857, "mtime": 1754942357181, "results": "114", "hashOfConfig": "67"}, {"size": 25889, "mtime": 1742943667842, "results": "115", "hashOfConfig": "67"}, {"size": 29956, "mtime": 1754672945896, "results": "116", "hashOfConfig": "67"}, {"size": 37983, "mtime": 1754858963346, "results": "117", "hashOfConfig": "67"}, {"size": 295, "mtime": 1743101819804, "results": "118", "hashOfConfig": "67"}, {"size": 23208, "mtime": 1742991622871, "results": "119", "hashOfConfig": "67"}, {"size": 25861, "mtime": 1755034793150, "results": "120", "hashOfConfig": "67"}, {"size": 40654, "mtime": 1742991622874, "results": "121", "hashOfConfig": "67"}, {"size": 3969, "mtime": 1742671375144, "results": "122", "hashOfConfig": "67"}, {"size": 4340, "mtime": 1742671396113, "results": "123", "hashOfConfig": "67"}, {"size": 2654, "mtime": 1742671423203, "results": "124", "hashOfConfig": "67"}, {"size": 2614, "mtime": 1742671410454, "results": "125", "hashOfConfig": "67"}, {"size": 4148, "mtime": 1742671355716, "results": "126", "hashOfConfig": "67"}, {"size": 4754, "mtime": 1754517017782, "results": "127", "hashOfConfig": "67"}, {"size": 18012, "mtime": 1754673063946, "results": "128", "hashOfConfig": "67"}, {"size": 16540, "mtime": 1755028366019, "results": "129", "hashOfConfig": "67"}, {"size": 13774, "mtime": 1754916816984, "results": "130", "hashOfConfig": "67"}, {"size": 19745, "mtime": 1755081909640, "results": "131", "hashOfConfig": "67"}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "t9pu2d", {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\index.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\App.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\contexts\\AuthContext.jsx", ["327"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\hooks\\useAuth.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\PagePermission.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ProtectedRoute.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\PageTransition.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\layout\\MainLayout.jsx", ["328"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\layout\\AuthLayout.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\services\\api.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\auth\\LoginPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\auth\\RegisterPage.jsx", ["329"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\auth\\ForgotPasswordPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\dashboard\\DashboardPage.jsx", ["330", "331", "332"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\profile\\ProfilePage.jsx", ["333"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\dashboard\\ExpertDashboardPage.jsx", ["334", "335", "336", "337", "338"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\profile\\ExpertProfilePage.jsx", ["339"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\users\\UsersPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\profile\\ClientProfilePage.jsx", ["340", "341"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\users\\UserFormPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\RolesPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\RoleFormPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\permissions\\PermissionsPage.jsx", ["342", "343"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\RolePermissionsPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\audit-logs\\AuditLogsPage.jsx", ["344"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\settings\\SystemSettingsPage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\profile\\AdminProfilePage.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\availabilities\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\sessions\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\appointments\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\messages\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\reports\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\clients\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\dashboard\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\experts\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\appointments\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\messages\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\sessions\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\packages\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\payments\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\availabilities\\AvailabilityPage.jsx", ["345", "346", "347", "348", "349", "350"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\sessions\\SessionsPage.jsx", ["351", "352", "353", "354", "355", "356", "357", "358", "359", "360"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\appointments\\AppointmentsPage.jsx", ["361", "362", "363", "364", "365", "366", "367"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\messages\\MessagesPage.jsx", ["368", "369", "370", "371", "372", "373", "374", "375", "376", "377"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\reports\\ReportsPage.jsx", ["378", "379", "380"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\expert\\clients\\ClientsPage.jsx", ["381", "382", "383", "384", "385"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\admin\\roles\\components\\PermissionMatrix.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\appointments\\ClientAppointmentsPage.jsx", ["386", "387", "388", "389", "390", "391", "392", "393", "394", "395"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\dashboard\\ClientDashboardPage.jsx", ["396", "397", "398", "399"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\experts\\ClientExpertsPage.jsx", ["400", "401", "402", "403", "404"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\messages\\ClientMessagesPage.jsx", ["405", "406", "407", "408", "409", "410", "411", "412", "413"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\index.js", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\packages\\ClientPackagesPage.jsx", ["414", "415"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\sessions\\ClientSessionsPage.jsx", ["416", "417", "418", "419", "420", "421", "422", "423", "424", "425"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\payments\\ClientPaymentsPage.jsx", ["426", "427", "428"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormTextarea.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormSelect.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormRadio.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormCheckbox.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\ui\\FormInput.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\common\\MultiSelect.jsx", [], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\experts\\ClientExpertDetailPage.jsx", ["429", "430", "431", "432", "433"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\client\\appointments\\BookAppointmentPage.jsx", ["434"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\components\\notifications\\NotificationCenter.jsx", ["435", "436", "437"], [], "C:\\Projeler\\kidgarden\\burky_root_web\\client\\src\\pages\\meeting\\MeetingPage.jsx", ["438", "439"], [], {"ruleId": "440", "severity": 1, "message": "441", "line": 75, "column": 6, "nodeType": "442", "endLine": 75, "endColumn": 8, "suggestions": "443"}, {"ruleId": "444", "severity": 1, "message": "445", "line": 1, "column": 27, "nodeType": "446", "messageId": "447", "endLine": 1, "endColumn": 36}, {"ruleId": "444", "severity": 1, "message": "448", "line": 11, "column": 21, "nodeType": "446", "messageId": "447", "endLine": 11, "endColumn": 33}, {"ruleId": "449", "severity": 1, "message": "450", "line": 30, "column": 15, "nodeType": "451", "endLine": 30, "endColumn": 91}, {"ruleId": "449", "severity": 1, "message": "450", "line": 57, "column": 15, "nodeType": "451", "endLine": 57, "endColumn": 91}, {"ruleId": "449", "severity": 1, "message": "450", "line": 84, "column": 15, "nodeType": "451", "endLine": 84, "endColumn": 91}, {"ruleId": "444", "severity": 1, "message": "452", "line": 6, "column": 21, "nodeType": "446", "messageId": "447", "endLine": 6, "endColumn": 33}, {"ruleId": "444", "severity": 1, "message": "453", "line": 12, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 12, "endColumn": 19}, {"ruleId": "444", "severity": 1, "message": "454", "line": 14, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 14, "endColumn": 16}, {"ruleId": "444", "severity": 1, "message": "455", "line": 29, "column": 34, "nodeType": "446", "messageId": "447", "endLine": 29, "endColumn": 59}, {"ruleId": "444", "severity": 1, "message": "456", "line": 130, "column": 54, "nodeType": "446", "messageId": "447", "endLine": 130, "endColumn": 61}, {"ruleId": "444", "severity": 1, "message": "457", "line": 133, "column": 9, "nodeType": "446", "messageId": "447", "endLine": 133, "endColumn": 23}, {"ruleId": "444", "severity": 1, "message": "458", "line": 29, "column": 10, "nodeType": "446", "messageId": "447", "endLine": 29, "endColumn": 20}, {"ruleId": "444", "severity": 1, "message": "452", "line": 6, "column": 47, "nodeType": "446", "messageId": "447", "endLine": 6, "endColumn": 59}, {"ruleId": "444", "severity": 1, "message": "459", "line": 24, "column": 10, "nodeType": "446", "messageId": "447", "endLine": 24, "endColumn": 20}, {"ruleId": "444", "severity": 1, "message": "460", "line": 9, "column": 11, "nodeType": "446", "messageId": "447", "endLine": 9, "endColumn": 24}, {"ruleId": "444", "severity": 1, "message": "461", "line": 12, "column": 10, "nodeType": "446", "messageId": "447", "endLine": 12, "endColumn": 15}, {"ruleId": "444", "severity": 1, "message": "460", "line": 9, "column": 11, "nodeType": "446", "messageId": "447", "endLine": 9, "endColumn": 24}, {"ruleId": "444", "severity": 1, "message": "462", "line": 16, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 16, "endColumn": 20}, {"ruleId": "444", "severity": 1, "message": "463", "line": 19, "column": 51, "nodeType": "446", "messageId": "447", "endLine": 19, "endColumn": 58}, {"ruleId": "444", "severity": 1, "message": "464", "line": 28, "column": 11, "nodeType": "446", "messageId": "447", "endLine": 28, "endColumn": 15}, {"ruleId": "444", "severity": 1, "message": "465", "line": 42, "column": 10, "nodeType": "446", "messageId": "447", "endLine": 42, "endColumn": 20}, {"ruleId": "440", "severity": 1, "message": "466", "line": 54, "column": 6, "nodeType": "442", "endLine": 54, "endColumn": 8, "suggestions": "467"}, {"ruleId": "444", "severity": 1, "message": "468", "line": 397, "column": 9, "nodeType": "446", "messageId": "447", "endLine": 397, "endColumn": 22}, {"ruleId": "444", "severity": 1, "message": "469", "line": 9, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 9, "endColumn": 14}, {"ruleId": "444", "severity": 1, "message": "470", "line": 11, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 11, "endColumn": 15}, {"ruleId": "444", "severity": 1, "message": "471", "line": 15, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 15, "endColumn": 11}, {"ruleId": "444", "severity": 1, "message": "472", "line": 16, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 16, "endColumn": 17}, {"ruleId": "444", "severity": 1, "message": "473", "line": 17, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 17, "endColumn": 16}, {"ruleId": "444", "severity": 1, "message": "474", "line": 18, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 18, "endColumn": 24}, {"ruleId": "444", "severity": 1, "message": "464", "line": 30, "column": 11, "nodeType": "446", "messageId": "447", "endLine": 30, "endColumn": 15}, {"ruleId": "444", "severity": 1, "message": "475", "line": 36, "column": 10, "nodeType": "446", "messageId": "447", "endLine": 36, "endColumn": 27}, {"ruleId": "444", "severity": 1, "message": "476", "line": 36, "column": 29, "nodeType": "446", "messageId": "447", "endLine": 36, "endColumn": 49}, {"ruleId": "440", "severity": 1, "message": "477", "line": 49, "column": 6, "nodeType": "442", "endLine": 49, "endColumn": 8, "suggestions": "478"}, {"ruleId": "444", "severity": 1, "message": "479", "line": 4, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 4, "endColumn": 14}, {"ruleId": "444", "severity": 1, "message": "480", "line": 7, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 7, "endColumn": 11}, {"ruleId": "444", "severity": 1, "message": "470", "line": 11, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 11, "endColumn": 15}, {"ruleId": "444", "severity": 1, "message": "472", "line": 15, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 15, "endColumn": 17}, {"ruleId": "444", "severity": 1, "message": "473", "line": 16, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 16, "endColumn": 16}, {"ruleId": "444", "severity": 1, "message": "474", "line": 17, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 17, "endColumn": 24}, {"ruleId": "444", "severity": 1, "message": "464", "line": 29, "column": 11, "nodeType": "446", "messageId": "447", "endLine": 29, "endColumn": 15}, {"ruleId": "444", "severity": 1, "message": "480", "line": 9, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 9, "endColumn": 11}, {"ruleId": "444", "severity": 1, "message": "481", "line": 13, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 13, "endColumn": 17}, {"ruleId": "444", "severity": 1, "message": "482", "line": 18, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 18, "endColumn": 12}, {"ruleId": "444", "severity": 1, "message": "483", "line": 20, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 20, "endColumn": 12}, {"ruleId": "444", "severity": 1, "message": "484", "line": 21, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 21, "endColumn": 12}, {"ruleId": "444", "severity": 1, "message": "485", "line": 27, "column": 10, "nodeType": "446", "messageId": "447", "endLine": 27, "endColumn": 14}, {"ruleId": "444", "severity": 1, "message": "486", "line": 43, "column": 10, "nodeType": "446", "messageId": "447", "endLine": 43, "endColumn": 21}, {"ruleId": "440", "severity": 1, "message": "487", "line": 276, "column": 6, "nodeType": "442", "endLine": 276, "endColumn": 23, "suggestions": "488"}, {"ruleId": "440", "severity": 1, "message": "489", "line": 281, "column": 6, "nodeType": "442", "endLine": 281, "endColumn": 8, "suggestions": "490"}, {"ruleId": "440", "severity": 1, "message": "487", "line": 342, "column": 6, "nodeType": "442", "endLine": 342, "endColumn": 28, "suggestions": "491"}, {"ruleId": "444", "severity": 1, "message": "480", "line": 13, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 13, "endColumn": 11}, {"ruleId": "444", "severity": 1, "message": "492", "line": 15, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 15, "endColumn": 28}, {"ruleId": "444", "severity": 1, "message": "464", "line": 256, "column": 11, "nodeType": "446", "messageId": "447", "endLine": 256, "endColumn": 15}, {"ruleId": "444", "severity": 1, "message": "462", "line": 11, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 11, "endColumn": 20}, {"ruleId": "444", "severity": 1, "message": "493", "line": 12, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 12, "endColumn": 17}, {"ruleId": "444", "severity": 1, "message": "480", "line": 13, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 13, "endColumn": 11}, {"ruleId": "444", "severity": 1, "message": "464", "line": 26, "column": 11, "nodeType": "446", "messageId": "447", "endLine": 26, "endColumn": 15}, {"ruleId": "444", "severity": 1, "message": "494", "line": 72, "column": 9, "nodeType": "446", "messageId": "447", "endLine": 72, "endColumn": 19}, {"ruleId": "444", "severity": 1, "message": "470", "line": 11, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 11, "endColumn": 15}, {"ruleId": "444", "severity": 1, "message": "495", "line": 12, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 12, "endColumn": 26}, {"ruleId": "444", "severity": 1, "message": "462", "line": 14, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 14, "endColumn": 20}, {"ruleId": "444", "severity": 1, "message": "472", "line": 15, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 15, "endColumn": 17}, {"ruleId": "444", "severity": 1, "message": "473", "line": 16, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 16, "endColumn": 16}, {"ruleId": "444", "severity": 1, "message": "474", "line": 17, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 17, "endColumn": 24}, {"ruleId": "444", "severity": 1, "message": "496", "line": 19, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 19, "endColumn": 28}, {"ruleId": "444", "severity": 1, "message": "464", "line": 31, "column": 11, "nodeType": "446", "messageId": "447", "endLine": 31, "endColumn": 15}, {"ruleId": "444", "severity": 1, "message": "497", "line": 151, "column": 9, "nodeType": "446", "messageId": "447", "endLine": 151, "endColumn": 24}, {"ruleId": "444", "severity": 1, "message": "494", "line": 171, "column": 9, "nodeType": "446", "messageId": "447", "endLine": 171, "endColumn": 19}, {"ruleId": "444", "severity": 1, "message": "470", "line": 5, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 5, "endColumn": 15}, {"ruleId": "444", "severity": 1, "message": "453", "line": 12, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 12, "endColumn": 19}, {"ruleId": "444", "severity": 1, "message": "454", "line": 14, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 14, "endColumn": 16}, {"ruleId": "444", "severity": 1, "message": "455", "line": 28, "column": 34, "nodeType": "446", "messageId": "447", "endLine": 28, "endColumn": 59}, {"ruleId": "444", "severity": 1, "message": "498", "line": 6, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 6, "endColumn": 18}, {"ruleId": "444", "severity": 1, "message": "499", "line": 10, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 10, "endColumn": 29}, {"ruleId": "444", "severity": 1, "message": "500", "line": 12, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 12, "endColumn": 18}, {"ruleId": "444", "severity": 1, "message": "501", "line": 13, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 13, "endColumn": 16}, {"ruleId": "444", "severity": 1, "message": "464", "line": 24, "column": 11, "nodeType": "446", "messageId": "447", "endLine": 24, "endColumn": 15}, {"ruleId": "444", "severity": 1, "message": "481", "line": 13, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 13, "endColumn": 17}, {"ruleId": "444", "severity": 1, "message": "502", "line": 15, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 15, "endColumn": 12}, {"ruleId": "444", "severity": 1, "message": "503", "line": 17, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 17, "endColumn": 24}, {"ruleId": "444", "severity": 1, "message": "483", "line": 20, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 20, "endColumn": 12}, {"ruleId": "444", "severity": 1, "message": "484", "line": 21, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 21, "endColumn": 12}, {"ruleId": "444", "severity": 1, "message": "486", "line": 45, "column": 10, "nodeType": "446", "messageId": "447", "endLine": 45, "endColumn": 21}, {"ruleId": "440", "severity": 1, "message": "487", "line": 280, "column": 6, "nodeType": "442", "endLine": 280, "endColumn": 23, "suggestions": "504"}, {"ruleId": "440", "severity": 1, "message": "489", "line": 285, "column": 6, "nodeType": "442", "endLine": 285, "endColumn": 8, "suggestions": "505"}, {"ruleId": "440", "severity": 1, "message": "487", "line": 347, "column": 6, "nodeType": "442", "endLine": 347, "endColumn": 28, "suggestions": "506"}, {"ruleId": "444", "severity": 1, "message": "464", "line": 20, "column": 11, "nodeType": "446", "messageId": "447", "endLine": 20, "endColumn": 15}, {"ruleId": "449", "severity": 1, "message": "450", "line": 487, "column": 19, "nodeType": "451", "endLine": 490, "endColumn": 20}, {"ruleId": "444", "severity": 1, "message": "469", "line": 9, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 9, "endColumn": 14}, {"ruleId": "444", "severity": 1, "message": "507", "line": 10, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 10, "endColumn": 11}, {"ruleId": "444", "severity": 1, "message": "470", "line": 11, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 11, "endColumn": 15}, {"ruleId": "444", "severity": 1, "message": "462", "line": 14, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 14, "endColumn": 20}, {"ruleId": "444", "severity": 1, "message": "471", "line": 15, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 15, "endColumn": 11}, {"ruleId": "444", "severity": 1, "message": "473", "line": 17, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 17, "endColumn": 16}, {"ruleId": "444", "severity": 1, "message": "464", "line": 31, "column": 11, "nodeType": "446", "messageId": "447", "endLine": 31, "endColumn": 15}, {"ruleId": "440", "severity": 1, "message": "477", "line": 80, "column": 6, "nodeType": "442", "endLine": 80, "endColumn": 8, "suggestions": "508"}, {"ruleId": "444", "severity": 1, "message": "509", "line": 164, "column": 9, "nodeType": "446", "messageId": "447", "endLine": 164, "endColumn": 14}, {"ruleId": "444", "severity": 1, "message": "497", "line": 233, "column": 9, "nodeType": "446", "messageId": "447", "endLine": 233, "endColumn": 24}, {"ruleId": "444", "severity": 1, "message": "510", "line": 7, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 7, "endColumn": 18}, {"ruleId": "444", "severity": 1, "message": "464", "line": 25, "column": 11, "nodeType": "446", "messageId": "447", "endLine": 25, "endColumn": 15}, {"ruleId": "444", "severity": 1, "message": "511", "line": 234, "column": 9, "nodeType": "446", "messageId": "447", "endLine": 234, "endColumn": 23}, {"ruleId": "444", "severity": 1, "message": "501", "line": 11, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 11, "endColumn": 16}, {"ruleId": "444", "severity": 1, "message": "512", "line": 15, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 15, "endColumn": 15}, {"ruleId": "444", "severity": 1, "message": "502", "line": 16, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 16, "endColumn": 12}, {"ruleId": "444", "severity": 1, "message": "513", "line": 17, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 17, "endColumn": 15}, {"ruleId": "444", "severity": 1, "message": "464", "line": 27, "column": 11, "nodeType": "446", "messageId": "447", "endLine": 27, "endColumn": 15}, {"ruleId": "440", "severity": 1, "message": "514", "line": 39, "column": 6, "nodeType": "442", "endLine": 39, "endColumn": 26, "suggestions": "515"}, {"ruleId": "444", "severity": 1, "message": "516", "line": 5, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 5, "endColumn": 12}, {"ruleId": "444", "severity": 1, "message": "480", "line": 9, "column": 3, "nodeType": "446", "messageId": "447", "endLine": 9, "endColumn": 11}, {"ruleId": "444", "severity": 1, "message": "517", "line": 13, "column": 10, "nodeType": "446", "messageId": "447", "endLine": 13, "endColumn": 16}, {"ruleId": "444", "severity": 1, "message": "460", "line": 24, "column": 17, "nodeType": "446", "messageId": "447", "endLine": 24, "endColumn": 30}, {"ruleId": "440", "severity": 1, "message": "518", "line": 68, "column": 6, "nodeType": "442", "endLine": 68, "endColumn": 31, "suggestions": "519"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchUserPermissions'. Either include it or remove the dependency array.", "ArrayExpression", ["520"], "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'registerUser' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'FormCheckbox' is defined but never used.", "'DocumentTextIcon' is defined but never used.", "'ArrowDownIcon' is defined but never used.", "'setHasUnreadNotifications' is assigned a value but never used.", "'clients' is assigned a value but never used.", "'getStatusColor' is assigned a value but never used.", "'expertData' is assigned a value but never used.", "'clientData' is assigned a value but never used.", "'hasPermission' is assigned a value but never used.", "'pages' is assigned a value but never used.", "'ArrowDownTrayIcon' is defined but never used.", "'addDays' is defined but never used.", "'user' is assigned a value but never used.", "'hasChanges' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAvailabilities'. Either include it or remove the dependency array.", ["521"], "'formatDayName' is assigned a value but never used.", "'XCircleIcon' is defined but never used.", "'ChartBarIcon' is defined but never used.", "'StarIcon' is defined but never used.", "'PlayCircleIcon' is defined but never used.", "'PaperClipIcon' is defined but never used.", "'DocumentArrowDownIcon' is defined but never used.", "'isCreatingMeeting' is assigned a value but never used.", "'setIsCreatingMeeting' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadSessions'. Either include it or remove the dependency array.", ["522"], "'VideoCamera' is defined but never used.", "'UserIcon' is defined but never used.", "'UserCircleIcon' is defined but never used.", "'ClockIcon' is defined but never used.", "'XMarkIcon' is defined but never used.", "'TrashIcon' is defined but never used.", "'Link' is defined but never used.", "'typingUsers' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'markConversationAsRead'. Either include it or remove the dependency array.", ["523"], "React Hook useEffect has a missing dependency: 'loadConversations'. Either include it or remove the dependency array.", ["524"], ["525"], "'PresentationChartLineIcon' is defined but never used.", "'CheckBadgeIcon' is defined but never used.", "'formatDate' is assigned a value but never used.", "'ChatBubbleLeftRightIcon' is defined but never used.", "'AdjustmentsHorizontalIcon' is defined but never used.", "'getStatusBorder' is assigned a value but never used.", "'ChevronDownIcon' is defined but never used.", "'ChatBubbleLeftEllipsisIcon' is defined but never used.", "'AcademicCapIcon' is defined but never used.", "'UserGroupIcon' is defined but never used.", "'PhoneIcon' is defined but never used.", "'InformationCircleIcon' is defined but never used.", ["526"], ["527"], ["528"], "'BellIcon' is defined but never used.", ["529"], "'today' is assigned a value but never used.", "'CheckCircleIcon' is defined but never used.", "'formatDateTime' is assigned a value but never used.", "'GlobeAltIcon' is defined but never used.", "'EnvelopeIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadExpertAndAvailability'. Either include it or remove the dependency array.", ["530"], "'CheckIcon' is defined but never used.", "'format' is defined but never used.", "React Hook useEffect has missing dependencies: 'cleanup' and 'initializeConnection'. Either include them or remove the dependency array.", ["531"], {"desc": "532", "fix": "533"}, {"desc": "534", "fix": "535"}, {"desc": "536", "fix": "537"}, {"desc": "538", "fix": "539"}, {"desc": "540", "fix": "541"}, {"desc": "542", "fix": "543"}, {"desc": "538", "fix": "544"}, {"desc": "540", "fix": "545"}, {"desc": "542", "fix": "546"}, {"desc": "536", "fix": "547"}, {"desc": "548", "fix": "549"}, {"desc": "550", "fix": "551"}, "Update the dependencies array to be: [fetchUserPermissions]", {"range": "552", "text": "553"}, "Update the dependencies array to be: [fetchAvailabilities]", {"range": "554", "text": "555"}, "Update the dependencies array to be: [loadSessions]", {"range": "556", "text": "557"}, "Update the dependencies array to be: [markConversationAsRead, socket, user.id]", {"range": "558", "text": "559"}, "Update the dependencies array to be: [loadConversations]", {"range": "560", "text": "561"}, "Update the dependencies array to be: [markConversationAsRead, selectedConversation]", {"range": "562", "text": "563"}, {"range": "564", "text": "559"}, {"range": "565", "text": "561"}, {"range": "566", "text": "563"}, {"range": "567", "text": "557"}, "Update the dependencies array to be: [expertId, loadExpertAndAvailability, navigate]", {"range": "568", "text": "569"}, "Update the dependencies array to be: [user.id, appointmentId, initializeConnection, cleanup]", {"range": "570", "text": "571"}, [2597, 2599], "[fetchUserPermissions]", [1761, 1763], "[fetchAvailabilities]", [1323, 1325], "[loadSessions]", [10531, 10548], "[markConversationAsRead, socket, user.id]", [10646, 10648], "[loadConversations]", [13050, 13072], "[markConversationAsRead, selectedConversation]", [10699, 10716], [10814, 10816], [13321, 13343], [2336, 2338], [1208, 1228], "[expertId, loadExpertAndAvailability, navigate]", [2236, 2261], "[user.id, appointmentId, initializeConnection, cleanup]"]