import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  MicrophoneIcon,
  VideoCameraIcon,
  PhoneXMarkIcon,
  SpeakerWaveIcon
} from '@heroicons/react/24/outline';
import {
  MicrophoneIcon as MicrophoneIconSolid,
  VideoCameraIcon as VideoCameraIconSolid,
  SpeakerWaveIcon as SpeakerWaveIconSolid
} from '@heroicons/react/24/solid';
import io from 'socket.io-client';
import toast from 'react-hot-toast';
import { useAuth } from '../../hooks/useAuth';

/**
 * P2P WebRTC Meeting Sayfası
 */
const MeetingPage = () => {
  const { appointmentId } = useParams();
  const navigate = useNavigate();
  const { user, hasPermission } = useAuth();

  // Permission check is now handled by PagePermission component
  useEffect(() => {
    console.log('🔍 MeetingPage initialized for appointment:', appointmentId);
    console.log('👤 User:', user ? { id: user.id, role: user.role?.name } : 'Not logged in');
  }, [user, appointmentId]);
  
  // State
  const [isConnected, setIsConnected] = useState(false);
  const [isAudioMuted, setIsAudioMuted] = useState(false);
  const [isVideoMuted, setIsVideoMuted] = useState(false);
  const [isSpeakerOn, setIsSpeakerOn] = useState(true);
  const [connectionStatus, setConnectionStatus] = useState('Bağlanıyor...');
  const [participantCount, setParticipantCount] = useState(0);
  const [isInitiator, setIsInitiator] = useState(false);
  
  // Refs
  const localVideoRef = useRef(null);
  const remoteVideoRef = useRef(null);
  const socketRef = useRef(null);
  const peerConnectionRef = useRef(null);
  const localStreamRef = useRef(null);
  const hasJoinedRoom = useRef(false);
  
  // WebRTC Configuration
  const rtcConfig = {
    iceServers: [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' },
    ],
  };

  useEffect(() => {
    // Sadece user ve appointmentId varsa bağlantı kur
    if (user?.id && appointmentId) {
      console.log('🚀 MEETING: Bağlantı başlatılıyor...', { userId: user.id, appointmentId });
      initializeConnection();
    }

    return () => {
      console.log('🧹 MEETING: Component unmount, cleanup çağrılıyor...');
      cleanup();
    };
  }, [user?.id, appointmentId]);

  const initializeConnection = async () => {
    try {
      // Socket.IO bağlantısı - diğer sayfalarla tutarlı URL kullan
      const socketUrl = process.env.REACT_APP_API_URL || 'https://192.168.1.111:5000';
      console.log('🔌 MEETING: Socket.IO bağlantısı deneniyor:', socketUrl);

      // Token'ı al (authentication için)
      const token = localStorage.getItem('accessToken');

      socketRef.current = io(socketUrl, {
        auth: { token },
        transports: ['websocket', 'polling'],
        timeout: 10000,
        reconnection: true,
        reconnectionDelay: 1000,
        reconnectionAttempts: 5,
        forceNew: false, // Mevcut bağlantıyı yeniden kullan
        rejectUnauthorized: false, // Self-signed sertifika için
        secure: true,
        upgrade: true,
        rememberUpgrade: false
      });
      
      // Socket event listeners
      setupSocketListeners();
      
      // Kullanıcı medyasını al
      await getUserMedia();

      // Odaya katıl (sadece bir kez)
      if (!hasJoinedRoom.current) {
        const roomId = `appointment-${appointmentId}`;
        console.log('🚪 MEETING: Odaya katılım isteği gönderiliyor:', roomId);
        socketRef.current.emit('join', roomId);
        hasJoinedRoom.current = true;
      } else {
        console.log('⚠️ MEETING: Zaten odaya katılım yapılmış, tekrar katılım atlanıyor');
      }
      
    } catch (error) {
      console.error('Bağlantı hatası:', error);
      toast.error('Bağlantı kurulamadı');
      setConnectionStatus('Bağlantı hatası');
    }
  };

  const setupSocketListeners = () => {
    const socket = socketRef.current;

    // Connection events
    socket.on('connect', () => {
      console.log('✅ MEETING: Socket.IO bağlantısı kuruldu:', socket.id);
      setConnectionStatus('Bağlandı, odaya katılıyor...');
    });

    socket.on('disconnect', (reason) => {
      console.log('❌ MEETING: Socket.IO bağlantısı kesildi:', reason);
      setConnectionStatus('Bağlantı kesildi');
      setIsConnected(false);
    });

    socket.on('connect_error', (error) => {
      console.error('🔥 MEETING: Socket.IO bağlantı hatası:', error);
      setConnectionStatus('Bağlantı hatası');
    });

    socket.on('joined', ({ roomId, roomSize }) => {
      console.log(`✅ MEETING: Odaya katıldı: ${roomId}, Katılımcı sayısı: ${roomSize}`);
      setParticipantCount(roomSize);
      setConnectionStatus(`Odada ${roomSize} kişi`);

      if (roomSize === 1) {
        setConnectionStatus('Karşı tarafı bekleniyor...');
        setIsInitiator(false); // İlk katılan initiator değil
      } else if (roomSize === 2) {
        setIsInitiator(true); // İkinci katılan initiator olur
      }
    });

    socket.on('ready', () => {
      console.log('Oda hazır, WebRTC bağlantısı başlatılıyor');
      setConnectionStatus('Bağlantı kuruluyor...');

      // Her iki taraf da peer connection oluşturur
      createPeerConnection();

      // Sadece ikinci katılan (initiator) offer gönderir
      setTimeout(() => {
        if (isInitiator) {
          console.log('🚀 MEETING: Initiator olarak offer gönderiliyor...');
          createOffer();
        } else {
          console.log('⏳ MEETING: Offer bekleniyor...');
        }
      }, 500); // Kısa bir gecikme
    });

    socket.on('full', (roomId) => {
      console.log('Oda dolu:', roomId);
      toast.error('Görüşme odası dolu');
      setConnectionStatus('Oda dolu');
    });

    socket.on('offer', async (sdp) => {
      console.log('Offer alındı');
      await handleOffer(sdp);
    });

    socket.on('answer', async (sdp) => {
      console.log('Answer alındı');
      await handleAnswer(sdp);
    });

    socket.on('candidate', async (candidate) => {
      console.log('ICE candidate alındı');
      await handleCandidate(candidate);
    });

    socket.on('leave', () => {
      console.log('Karşı taraf ayrıldı');
      toast('Karşı taraf görüşmeden ayrıldı', { icon: 'ℹ️' });
      setConnectionStatus('Karşı taraf ayrıldı');
      setParticipantCount(1);

      // Remote video'yu temizle
      if (remoteVideoRef.current) {
        remoteVideoRef.current.srcObject = null;
      }
    });
  };

  const getUserMedia = async () => {
    try {
      // Eğer zaten stream varsa, yenisini alma
      if (localStreamRef.current) {
        console.log('📹 MEETING: Local stream zaten mevcut');
        return localStreamRef.current;
      }

      console.log('📹 MEETING: Kullanıcı medyası isteniyor...');
      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true
      });

      localStreamRef.current = stream;
      if (localVideoRef.current) {
        localVideoRef.current.srcObject = stream;
      }

      console.log('✅ MEETING: Kullanıcı medyası alındı');
      return stream;
    } catch (error) {
      console.error('❌ MEETING: Medya erişim hatası:', error);
      toast.error('Kamera ve mikrofon erişimi gerekli');
      throw error;
    }
  };

  const createPeerConnection = () => {
    // Eğer zaten peer connection varsa, yenisini oluşturma
    if (peerConnectionRef.current) {
      console.log('⚠️ MEETING: Peer connection zaten mevcut, yenisi oluşturulmuyor');
      return peerConnectionRef.current;
    }

    console.log('🔗 MEETING: Yeni peer connection oluşturuluyor...');
    const pc = new RTCPeerConnection(rtcConfig);
    peerConnectionRef.current = pc;

    // ICE candidate event
    pc.onicecandidate = (event) => {
      if (event.candidate) {
        socketRef.current.emit('candidate', {
          roomId: `appointment-${appointmentId}`,
          candidate: event.candidate
        });
      }
    };

    // Remote stream event
    pc.ontrack = (event) => {
      console.log('Remote stream alındı');
      const [remoteStream] = event.streams;
      if (remoteVideoRef.current) {
        remoteVideoRef.current.srcObject = remoteStream;
      }
      setIsConnected(true);
      setConnectionStatus('Bağlantı kuruldu');
    };

    // Connection state change
    pc.onconnectionstatechange = () => {
      console.log('Connection state:', pc.connectionState);
      if (pc.connectionState === 'connected') {
        setIsConnected(true);
        setConnectionStatus('Bağlantı kuruldu');
      } else if (pc.connectionState === 'disconnected') {
        setIsConnected(false);
        setConnectionStatus('Bağlantı kesildi');
      }
    };

    // Local stream'i peer connection'a ekle
    if (localStreamRef.current) {
      localStreamRef.current.getTracks().forEach(track => {
        pc.addTrack(track, localStreamRef.current);
      });
    }
  };

  const createOffer = async () => {
    try {
      const pc = peerConnectionRef.current;

      if (!pc) {
        console.error('❌ MEETING: Peer connection bulunamadı');
        return;
      }

      if (pc.signalingState !== 'stable') {
        console.log(`⚠️ MEETING: Yanlış signaling state: ${pc.signalingState}, offer atlanıyor`);
        return;
      }

      console.log('📤 MEETING: Offer oluşturuluyor...');
      const offer = await pc.createOffer({
        offerToReceiveAudio: true,
        offerToReceiveVideo: true
      });

      console.log('🔄 MEETING: Local description ayarlanıyor...');
      await pc.setLocalDescription(offer);

      console.log('📤 MEETING: Offer gönderiliyor...');
      socketRef.current.emit('offer', {
        roomId: `appointment-${appointmentId}`,
        sdp: offer
      });
    } catch (error) {
      console.error('❌ MEETING: Offer oluşturma hatası:', error);
    }
  };

  const handleOffer = async (sdp) => {
    try {
      console.log('📥 MEETING: Offer alındı, işleniyor...');

      // Peer connection yoksa oluştur
      if (!peerConnectionRef.current) {
        createPeerConnection();
      }

      const pc = peerConnectionRef.current;

      // State kontrolü
      if (pc.signalingState !== 'stable' && pc.signalingState !== 'have-local-offer') {
        console.log(`⚠️ MEETING: Yanlış signaling state: ${pc.signalingState}, offer atlanıyor`);
        return;
      }

      console.log('🔄 MEETING: Remote description ayarlanıyor...');
      await pc.setRemoteDescription(new RTCSessionDescription(sdp));

      console.log('📤 MEETING: Answer oluşturuluyor...');
      const answer = await pc.createAnswer();
      await pc.setLocalDescription(answer);

      console.log('📤 MEETING: Answer gönderiliyor...');
      socketRef.current.emit('answer', {
        roomId: `appointment-${appointmentId}`,
        sdp: answer
      });
    } catch (error) {
      console.error('❌ MEETING: Offer işleme hatası:', error);
    }
  };

  const handleAnswer = async (sdp) => {
    try {
      console.log('📥 MEETING: Answer alındı, işleniyor...');
      const pc = peerConnectionRef.current;

      if (!pc) {
        console.error('❌ MEETING: Peer connection bulunamadı');
        return;
      }

      // State kontrolü
      if (pc.signalingState !== 'have-local-offer') {
        console.log(`⚠️ MEETING: Yanlış signaling state: ${pc.signalingState}, answer atlanıyor`);
        return;
      }

      console.log('🔄 MEETING: Remote description (answer) ayarlanıyor...');
      await pc.setRemoteDescription(new RTCSessionDescription(sdp));
      console.log('✅ MEETING: Answer başarıyla işlendi');
    } catch (error) {
      console.error('❌ MEETING: Answer işleme hatası:', error);
    }
  };

  const handleCandidate = async (candidate) => {
    try {
      const pc = peerConnectionRef.current;
      if (!pc) {
        console.log('⚠️ MEETING: Peer connection yok, ICE candidate atlanıyor');
        return;
      }

      if (pc.remoteDescription) {
        console.log('🧊 MEETING: ICE candidate ekleniyor...');
        await pc.addIceCandidate(new RTCIceCandidate(candidate));
        console.log('✅ MEETING: ICE candidate başarıyla eklendi');
      } else {
        console.log('⚠️ MEETING: Remote description yok, ICE candidate atlanıyor');
      }
    } catch (error) {
      console.error('❌ MEETING: ICE candidate işleme hatası:', error);
    }
  };

  const toggleAudio = () => {
    if (localStreamRef.current) {
      const audioTrack = localStreamRef.current.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        setIsAudioMuted(!audioTrack.enabled);
      }
    }
  };

  const toggleVideo = () => {
    if (localStreamRef.current) {
      const videoTrack = localStreamRef.current.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled;
        setIsVideoMuted(!videoTrack.enabled);
      }
    }
  };

  const toggleSpeaker = () => {
    if (remoteVideoRef.current) {
      remoteVideoRef.current.muted = isSpeakerOn;
      setIsSpeakerOn(!isSpeakerOn);
    }
  };

  const endCall = () => {
    cleanup();

    // Kullanıcının rolüne göre doğru sayfaya yönlendir
    if (user.role?.name === 'Admin') {
      navigate('/admin/dashboard');
    } else if (user.role?.name === 'Expert') {
      navigate('/expert/sessions');
    } else if (user.role?.name === 'Client') {
      navigate('/client/sessions');
    } else {
      navigate('/');
    }

    toast.success('Görüşme sonlandırıldı');
  };

  const cleanup = () => {
    console.log('🧹 MEETING: Cleanup başlatılıyor...');

    // Socket bağlantısını kapat
    if (socketRef.current) {
      console.log('🔌 MEETING: Socket bağlantısı kapatılıyor...');
      socketRef.current.emit('leave', `appointment-${appointmentId}`);
      socketRef.current.off(); // Tüm event listener'ları kaldır
      socketRef.current.disconnect();
      socketRef.current = null;
    }

    // Peer connection'ı kapat
    if (peerConnectionRef.current) {
      console.log('🔗 MEETING: Peer connection kapatılıyor...');
      peerConnectionRef.current.onicecandidate = null;
      peerConnectionRef.current.ontrack = null;
      peerConnectionRef.current.close();
      peerConnectionRef.current = null;
    }

    // Local stream'i durdur
    if (localStreamRef.current) {
      console.log('📹 MEETING: Local stream durduruluyor...');
      localStreamRef.current.getTracks().forEach(track => {
        track.stop();
        console.log(`📹 MEETING: Track durduruldu: ${track.kind}`);
      });
      localStreamRef.current = null;
    }

    // Video elementlerini temizle
    if (localVideoRef.current) {
      localVideoRef.current.srcObject = null;
    }
    if (remoteVideoRef.current) {
      remoteVideoRef.current.srcObject = null;
    }

    // State'leri sıfırla
    setIsConnected(false);
    setParticipantCount(0);
    setConnectionStatus('Bağlantı kesildi');
    setIsInitiator(false);
    hasJoinedRoom.current = false;

    console.log('✅ MEETING: Cleanup tamamlandı');
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <div className="bg-gray-800 px-6 py-4 border-b border-gray-700">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-xl font-semibold">Psikolojik Danışmanlık Seansı</h1>
            <p className="text-sm text-gray-400">Randevu #{appointmentId}</p>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-400">{connectionStatus}</p>
            <p className="text-xs text-gray-500">{participantCount} katılımcı</p>
          </div>
        </div>
      </div>

      {/* Video Container */}
      <div className="flex-1 p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-[calc(100vh-200px)]">
          {/* Local Video */}
          <div className="relative bg-gray-800 rounded-lg overflow-hidden">
            <video
              ref={localVideoRef}
              autoPlay
              playsInline
              muted
              className="w-full h-full object-cover"
            />
            <div className="absolute bottom-4 left-4 bg-black bg-opacity-50 px-3 py-1 rounded">
              <span className="text-sm">Siz</span>
            </div>
            {isVideoMuted && (
              <div className="absolute inset-0 bg-gray-700 flex items-center justify-center">
                <VideoCameraIconSolid className="h-16 w-16 text-gray-400" />
              </div>
            )}
          </div>

          {/* Remote Video */}
          <div className="relative bg-gray-800 rounded-lg overflow-hidden">
            <video
              ref={remoteVideoRef}
              autoPlay
              playsInline
              className="w-full h-full object-cover"
            />
            <div className="absolute bottom-4 left-4 bg-black bg-opacity-50 px-3 py-1 rounded">
              <span className="text-sm">Karşı Taraf</span>
            </div>
            {!isConnected && (
              <div className="absolute inset-0 bg-gray-700 flex items-center justify-center">
                <div className="text-center">
                  <VideoCameraIconSolid className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-400">Karşı taraf bekleniyor...</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2">
        <div className="flex space-x-4 bg-gray-800 rounded-full px-6 py-3 shadow-lg">
          {/* Mikrofon */}
          <button
            onClick={toggleAudio}
            className={`p-3 rounded-full transition-colors ${
              isAudioMuted 
                ? 'bg-red-600 hover:bg-red-700' 
                : 'bg-gray-600 hover:bg-gray-700'
            }`}
          >
            {isAudioMuted ? (
              <MicrophoneIconSolid className="h-6 w-6" />
            ) : (
              <MicrophoneIcon className="h-6 w-6" />
            )}
          </button>

          {/* Kamera */}
          <button
            onClick={toggleVideo}
            className={`p-3 rounded-full transition-colors ${
              isVideoMuted 
                ? 'bg-red-600 hover:bg-red-700' 
                : 'bg-gray-600 hover:bg-gray-700'
            }`}
          >
            {isVideoMuted ? (
              <VideoCameraIconSolid className="h-6 w-6" />
            ) : (
              <VideoCameraIcon className="h-6 w-6" />
            )}
          </button>

          {/* Hoparlör */}
          <button
            onClick={toggleSpeaker}
            className={`p-3 rounded-full transition-colors ${
              !isSpeakerOn 
                ? 'bg-red-600 hover:bg-red-700' 
                : 'bg-gray-600 hover:bg-gray-700'
            }`}
          >
            {isSpeakerOn ? (
              <SpeakerWaveIcon className="h-6 w-6" />
            ) : (
              <SpeakerWaveIconSolid className="h-6 w-6" />
            )}
          </button>

          {/* Görüşmeyi Sonlandır */}
          <button
            onClick={endCall}
            className="p-3 rounded-full bg-red-600 hover:bg-red-700 transition-colors"
          >
            <PhoneXMarkIcon className="h-6 w-6" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default MeetingPage;
