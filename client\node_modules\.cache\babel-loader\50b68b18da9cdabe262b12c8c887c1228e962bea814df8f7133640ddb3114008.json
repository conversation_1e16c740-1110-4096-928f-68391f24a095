{"ast": null, "code": "var _jsxFileName = \"C:\\\\Projeler\\\\kidgarden\\\\burky_root_web\\\\client\\\\src\\\\pages\\\\meeting\\\\MeetingPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { MicrophoneIcon, VideoCameraIcon, PhoneXMarkIcon, SpeakerWaveIcon } from '@heroicons/react/24/outline';\nimport { MicrophoneIcon as MicrophoneIconSolid, VideoCameraIcon as VideoCameraIconSolid, SpeakerWaveIcon as SpeakerWaveIconSolid } from '@heroicons/react/24/solid';\nimport io from 'socket.io-client';\nimport toast from 'react-hot-toast';\nimport { useAuth } from '../../hooks/useAuth';\n\n/**\n * P2P WebRTC Meeting Sayfası\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MeetingPage = () => {\n  _s();\n  const {\n    appointmentId\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    user,\n    hasPermission\n  } = useAuth();\n\n  // Permission check is now handled by PagePermission component\n  useEffect(() => {\n    var _user$role;\n    console.log('🔍 MeetingPage initialized for appointment:', appointmentId);\n    console.log('👤 User:', user ? {\n      id: user.id,\n      role: (_user$role = user.role) === null || _user$role === void 0 ? void 0 : _user$role.name\n    } : 'Not logged in');\n  }, [user, appointmentId]);\n\n  // State\n  const [isConnected, setIsConnected] = useState(false);\n  const [isAudioMuted, setIsAudioMuted] = useState(false);\n  const [isVideoMuted, setIsVideoMuted] = useState(false);\n  const [isSpeakerOn, setIsSpeakerOn] = useState(true);\n  const [connectionStatus, setConnectionStatus] = useState('Bağlanıyor...');\n  const [participantCount, setParticipantCount] = useState(0);\n  const [isInitiator, setIsInitiator] = useState(false);\n\n  // Refs\n  const localVideoRef = useRef(null);\n  const remoteVideoRef = useRef(null);\n  const socketRef = useRef(null);\n  const peerConnectionRef = useRef(null);\n  const localStreamRef = useRef(null);\n\n  // WebRTC Configuration\n  const rtcConfig = {\n    iceServers: [{\n      urls: 'stun:stun.l.google.com:19302'\n    }, {\n      urls: 'stun:stun1.l.google.com:19302'\n    }]\n  };\n  useEffect(() => {\n    // Sadece user ve appointmentId varsa bağlantı kur\n    if (user !== null && user !== void 0 && user.id && appointmentId) {\n      console.log('🚀 MEETING: Bağlantı başlatılıyor...', {\n        userId: user.id,\n        appointmentId\n      });\n      initializeConnection();\n    }\n    return () => {\n      console.log('🧹 MEETING: Component unmount, cleanup çağrılıyor...');\n      cleanup();\n    };\n  }, [user === null || user === void 0 ? void 0 : user.id, appointmentId]);\n  const initializeConnection = async () => {\n    try {\n      // Socket.IO bağlantısı - diğer sayfalarla tutarlı URL kullan\n      const socketUrl = process.env.REACT_APP_API_URL || 'https://192.168.1.111:5000';\n      console.log('🔌 MEETING: Socket.IO bağlantısı deneniyor:', socketUrl);\n\n      // Token'ı al (authentication için)\n      const token = localStorage.getItem('accessToken');\n      socketRef.current = io(socketUrl, {\n        auth: {\n          token\n        },\n        transports: ['websocket', 'polling'],\n        timeout: 10000,\n        reconnection: true,\n        reconnectionDelay: 1000,\n        reconnectionAttempts: 5,\n        forceNew: false,\n        // Mevcut bağlantıyı yeniden kullan\n        rejectUnauthorized: false,\n        // Self-signed sertifika için\n        secure: true,\n        upgrade: true,\n        rememberUpgrade: false\n      });\n\n      // Socket event listeners\n      setupSocketListeners();\n\n      // Kullanıcı medyasını al\n      await getUserMedia();\n\n      // Odaya katıl\n      const roomId = `appointment-${appointmentId}`;\n      socketRef.current.emit('join', roomId);\n    } catch (error) {\n      console.error('Bağlantı hatası:', error);\n      toast.error('Bağlantı kurulamadı');\n      setConnectionStatus('Bağlantı hatası');\n    }\n  };\n  const setupSocketListeners = () => {\n    const socket = socketRef.current;\n\n    // Connection events\n    socket.on('connect', () => {\n      console.log('✅ MEETING: Socket.IO bağlantısı kuruldu:', socket.id);\n      setConnectionStatus('Bağlandı, odaya katılıyor...');\n    });\n    socket.on('disconnect', reason => {\n      console.log('❌ MEETING: Socket.IO bağlantısı kesildi:', reason);\n      setConnectionStatus('Bağlantı kesildi');\n      setIsConnected(false);\n    });\n    socket.on('connect_error', error => {\n      console.error('🔥 MEETING: Socket.IO bağlantı hatası:', error);\n      setConnectionStatus('Bağlantı hatası');\n    });\n    socket.on('joined', ({\n      roomId,\n      roomSize\n    }) => {\n      console.log(`✅ MEETING: Odaya katıldı: ${roomId}, Katılımcı sayısı: ${roomSize}`);\n      setParticipantCount(roomSize);\n      setConnectionStatus(`Odada ${roomSize} kişi`);\n      if (roomSize === 1) {\n        setConnectionStatus('Karşı tarafı bekleniyor...');\n        setIsInitiator(false); // İlk katılan initiator değil\n      } else if (roomSize === 2) {\n        setIsInitiator(true); // İkinci katılan initiator olur\n      }\n    });\n    socket.on('ready', () => {\n      console.log('Oda hazır, WebRTC bağlantısı başlatılıyor');\n      setConnectionStatus('Bağlantı kuruluyor...');\n\n      // Sadece initiator peer connection oluşturur ve offer gönderir\n      if (isInitiator) {\n        console.log('🚀 MEETING: Initiator olarak offer gönderiliyor...');\n        createPeerConnection();\n        setTimeout(() => createOffer(), 1000); // Kısa bir gecikme\n      } else {\n        console.log('⏳ MEETING: Offer bekleniyor...');\n        createPeerConnection(); // Peer connection'ı hazırla ama offer gönderme\n      }\n    });\n    socket.on('full', roomId => {\n      console.log('Oda dolu:', roomId);\n      toast.error('Görüşme odası dolu');\n      setConnectionStatus('Oda dolu');\n    });\n    socket.on('offer', async sdp => {\n      console.log('Offer alındı');\n      await handleOffer(sdp);\n    });\n    socket.on('answer', async sdp => {\n      console.log('Answer alındı');\n      await handleAnswer(sdp);\n    });\n    socket.on('candidate', async candidate => {\n      console.log('ICE candidate alındı');\n      await handleCandidate(candidate);\n    });\n    socket.on('leave', () => {\n      console.log('Karşı taraf ayrıldı');\n      toast.info('Karşı taraf görüşmeden ayrıldı');\n      setConnectionStatus('Karşı taraf ayrıldı');\n      setParticipantCount(1);\n\n      // Remote video'yu temizle\n      if (remoteVideoRef.current) {\n        remoteVideoRef.current.srcObject = null;\n      }\n    });\n  };\n  const getUserMedia = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: true,\n        audio: true\n      });\n      localStreamRef.current = stream;\n      if (localVideoRef.current) {\n        localVideoRef.current.srcObject = stream;\n      }\n      console.log('Kullanıcı medyası alındı');\n    } catch (error) {\n      console.error('Medya erişim hatası:', error);\n      toast.error('Kamera ve mikrofon erişimi gerekli');\n    }\n  };\n  const createPeerConnection = () => {\n    // Eğer zaten peer connection varsa, yenisini oluşturma\n    if (peerConnectionRef.current) {\n      console.log('⚠️ MEETING: Peer connection zaten mevcut, yenisi oluşturulmuyor');\n      return peerConnectionRef.current;\n    }\n    console.log('🔗 MEETING: Yeni peer connection oluşturuluyor...');\n    const pc = new RTCPeerConnection(rtcConfig);\n    peerConnectionRef.current = pc;\n\n    // ICE candidate event\n    pc.onicecandidate = event => {\n      if (event.candidate) {\n        socketRef.current.emit('candidate', {\n          roomId: `appointment-${appointmentId}`,\n          candidate: event.candidate\n        });\n      }\n    };\n\n    // Remote stream event\n    pc.ontrack = event => {\n      console.log('Remote stream alındı');\n      const [remoteStream] = event.streams;\n      if (remoteVideoRef.current) {\n        remoteVideoRef.current.srcObject = remoteStream;\n      }\n      setIsConnected(true);\n      setConnectionStatus('Bağlantı kuruldu');\n    };\n\n    // Connection state change\n    pc.onconnectionstatechange = () => {\n      console.log('Connection state:', pc.connectionState);\n      if (pc.connectionState === 'connected') {\n        setIsConnected(true);\n        setConnectionStatus('Bağlantı kuruldu');\n      } else if (pc.connectionState === 'disconnected') {\n        setIsConnected(false);\n        setConnectionStatus('Bağlantı kesildi');\n      }\n    };\n\n    // Local stream'i peer connection'a ekle\n    if (localStreamRef.current) {\n      localStreamRef.current.getTracks().forEach(track => {\n        pc.addTrack(track, localStreamRef.current);\n      });\n    }\n  };\n  const createOffer = async () => {\n    try {\n      const pc = peerConnectionRef.current;\n      const offer = await pc.createOffer();\n      await pc.setLocalDescription(offer);\n      socketRef.current.emit('offer', {\n        roomId: `appointment-${appointmentId}`,\n        sdp: offer\n      });\n    } catch (error) {\n      console.error('Offer oluşturma hatası:', error);\n    }\n  };\n  const handleOffer = async sdp => {\n    try {\n      console.log('📥 MEETING: Offer alındı, işleniyor...');\n\n      // Peer connection yoksa oluştur\n      if (!peerConnectionRef.current) {\n        createPeerConnection();\n      }\n      const pc = peerConnectionRef.current;\n\n      // State kontrolü\n      if (pc.signalingState !== 'stable' && pc.signalingState !== 'have-local-offer') {\n        console.log(`⚠️ MEETING: Yanlış signaling state: ${pc.signalingState}, offer atlanıyor`);\n        return;\n      }\n      console.log('🔄 MEETING: Remote description ayarlanıyor...');\n      await pc.setRemoteDescription(new RTCSessionDescription(sdp));\n      console.log('📤 MEETING: Answer oluşturuluyor...');\n      const answer = await pc.createAnswer();\n      await pc.setLocalDescription(answer);\n      console.log('📤 MEETING: Answer gönderiliyor...');\n      socketRef.current.emit('answer', {\n        roomId: `appointment-${appointmentId}`,\n        sdp: answer\n      });\n    } catch (error) {\n      console.error('❌ MEETING: Offer işleme hatası:', error);\n    }\n  };\n  const handleAnswer = async sdp => {\n    try {\n      console.log('📥 MEETING: Answer alındı, işleniyor...');\n      const pc = peerConnectionRef.current;\n      if (!pc) {\n        console.error('❌ MEETING: Peer connection bulunamadı');\n        return;\n      }\n\n      // State kontrolü\n      if (pc.signalingState !== 'have-local-offer') {\n        console.log(`⚠️ MEETING: Yanlış signaling state: ${pc.signalingState}, answer atlanıyor`);\n        return;\n      }\n      console.log('🔄 MEETING: Remote description (answer) ayarlanıyor...');\n      await pc.setRemoteDescription(new RTCSessionDescription(sdp));\n      console.log('✅ MEETING: Answer başarıyla işlendi');\n    } catch (error) {\n      console.error('❌ MEETING: Answer işleme hatası:', error);\n    }\n  };\n  const handleCandidate = async candidate => {\n    try {\n      const pc = peerConnectionRef.current;\n      if (pc && pc.remoteDescription) {\n        await pc.addIceCandidate(candidate);\n      }\n    } catch (error) {\n      console.error('ICE candidate işleme hatası:', error);\n    }\n  };\n  const toggleAudio = () => {\n    if (localStreamRef.current) {\n      const audioTrack = localStreamRef.current.getAudioTracks()[0];\n      if (audioTrack) {\n        audioTrack.enabled = !audioTrack.enabled;\n        setIsAudioMuted(!audioTrack.enabled);\n      }\n    }\n  };\n  const toggleVideo = () => {\n    if (localStreamRef.current) {\n      const videoTrack = localStreamRef.current.getVideoTracks()[0];\n      if (videoTrack) {\n        videoTrack.enabled = !videoTrack.enabled;\n        setIsVideoMuted(!videoTrack.enabled);\n      }\n    }\n  };\n  const toggleSpeaker = () => {\n    if (remoteVideoRef.current) {\n      remoteVideoRef.current.muted = isSpeakerOn;\n      setIsSpeakerOn(!isSpeakerOn);\n    }\n  };\n  const endCall = () => {\n    var _user$role2, _user$role3, _user$role4;\n    cleanup();\n\n    // Kullanıcının rolüne göre doğru sayfaya yönlendir\n    if (((_user$role2 = user.role) === null || _user$role2 === void 0 ? void 0 : _user$role2.name) === 'Admin') {\n      navigate('/admin/dashboard');\n    } else if (((_user$role3 = user.role) === null || _user$role3 === void 0 ? void 0 : _user$role3.name) === 'Expert') {\n      navigate('/expert/sessions');\n    } else if (((_user$role4 = user.role) === null || _user$role4 === void 0 ? void 0 : _user$role4.name) === 'Client') {\n      navigate('/client/sessions');\n    } else {\n      navigate('/');\n    }\n    toast.success('Görüşme sonlandırıldı');\n  };\n  const cleanup = () => {\n    console.log('🧹 MEETING: Cleanup başlatılıyor...');\n\n    // Socket bağlantısını kapat\n    if (socketRef.current) {\n      console.log('🔌 MEETING: Socket bağlantısı kapatılıyor...');\n      socketRef.current.emit('leave', `appointment-${appointmentId}`);\n      socketRef.current.off(); // Tüm event listener'ları kaldır\n      socketRef.current.disconnect();\n      socketRef.current = null;\n    }\n\n    // Peer connection'ı kapat\n    if (peerConnectionRef.current) {\n      console.log('🔗 MEETING: Peer connection kapatılıyor...');\n      peerConnectionRef.current.onicecandidate = null;\n      peerConnectionRef.current.ontrack = null;\n      peerConnectionRef.current.close();\n      peerConnectionRef.current = null;\n    }\n\n    // Local stream'i durdur\n    if (localStreamRef.current) {\n      console.log('📹 MEETING: Local stream durduruluyor...');\n      localStreamRef.current.getTracks().forEach(track => {\n        track.stop();\n        console.log(`📹 MEETING: Track durduruldu: ${track.kind}`);\n      });\n      localStreamRef.current = null;\n    }\n\n    // Video elementlerini temizle\n    if (localVideoRef.current) {\n      localVideoRef.current.srcObject = null;\n    }\n    if (remoteVideoRef.current) {\n      remoteVideoRef.current.srcObject = null;\n    }\n\n    // State'leri sıfırla\n    setIsConnected(false);\n    setParticipantCount(0);\n    setConnectionStatus('Bağlantı kesildi');\n    console.log('✅ MEETING: Cleanup tamamlandı');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-900 text-white\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 px-6 py-4 border-b border-gray-700\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-xl font-semibold\",\n            children: \"Psikolojik Dan\\u0131\\u015Fmanl\\u0131k Seans\\u0131\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-400\",\n            children: [\"Randevu #\", appointmentId]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-right\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-400\",\n            children: connectionStatus\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-500\",\n            children: [participantCount, \" kat\\u0131l\\u0131mc\\u0131\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 441,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 440,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 h-[calc(100vh-200px)]\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative bg-gray-800 rounded-lg overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"video\", {\n            ref: localVideoRef,\n            autoPlay: true,\n            playsInline: true,\n            muted: true,\n            className: \"w-full h-full object-cover\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-4 left-4 bg-black bg-opacity-50 px-3 py-1 rounded\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm\",\n              children: \"Siz\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 13\n          }, this), isVideoMuted && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gray-700 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(VideoCameraIconSolid, {\n              className: \"h-16 w-16 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative bg-gray-800 rounded-lg overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"video\", {\n            ref: remoteVideoRef,\n            autoPlay: true,\n            playsInline: true,\n            className: \"w-full h-full object-cover\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-4 left-4 bg-black bg-opacity-50 px-3 py-1 rounded\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm\",\n              children: \"Kar\\u015F\\u0131 Taraf\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 13\n          }, this), !isConnected && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gray-700 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(VideoCameraIconSolid, {\n                className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-400\",\n                children: \"Kar\\u015F\\u0131 taraf bekleniyor...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 476,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 455,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 454,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-6 left-1/2 transform -translate-x-1/2\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-4 bg-gray-800 rounded-full px-6 py-3 shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: toggleAudio,\n          className: `p-3 rounded-full transition-colors ${isAudioMuted ? 'bg-red-600 hover:bg-red-700' : 'bg-gray-600 hover:bg-gray-700'}`,\n          children: isAudioMuted ? /*#__PURE__*/_jsxDEV(MicrophoneIconSolid, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(MicrophoneIcon, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 513,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: toggleVideo,\n          className: `p-3 rounded-full transition-colors ${isVideoMuted ? 'bg-red-600 hover:bg-red-700' : 'bg-gray-600 hover:bg-gray-700'}`,\n          children: isVideoMuted ? /*#__PURE__*/_jsxDEV(VideoCameraIconSolid, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(VideoCameraIcon, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: toggleSpeaker,\n          className: `p-3 rounded-full transition-colors ${!isSpeakerOn ? 'bg-red-600 hover:bg-red-700' : 'bg-gray-600 hover:bg-gray-700'}`,\n          children: isSpeakerOn ? /*#__PURE__*/_jsxDEV(SpeakerWaveIcon, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(SpeakerWaveIconSolid, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 534,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: endCall,\n          className: \"p-3 rounded-full bg-red-600 hover:bg-red-700 transition-colors\",\n          children: /*#__PURE__*/_jsxDEV(PhoneXMarkIcon, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 550,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 500,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 499,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 438,\n    columnNumber: 5\n  }, this);\n};\n_s(MeetingPage, \"SUKnTbV74a9pGtvScEfXZPFokRc=\", false, function () {\n  return [useParams, useNavigate, useAuth];\n});\n_c = MeetingPage;\nexport default MeetingPage;\nvar _c;\n$RefreshReg$(_c, \"MeetingPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useParams", "useNavigate", "MicrophoneIcon", "VideoCameraIcon", "PhoneXMarkIcon", "SpeakerWaveIcon", "MicrophoneIconSolid", "VideoCameraIconSolid", "SpeakerWaveIconSolid", "io", "toast", "useAuth", "jsxDEV", "_jsxDEV", "MeetingPage", "_s", "appointmentId", "navigate", "user", "hasPermission", "_user$role", "console", "log", "id", "role", "name", "isConnected", "setIsConnected", "isAudioMuted", "setIsAudioMuted", "isVideoMuted", "setIsVideoMuted", "isSpeakerOn", "setIsSpeakerOn", "connectionStatus", "setConnectionStatus", "participantCount", "setParticipantCount", "isInitiator", "setIsInitiator", "localVideoRef", "remoteVideoRef", "socketRef", "peerConnectionRef", "localStreamRef", "rtcConfig", "iceServers", "urls", "userId", "initializeConnection", "cleanup", "socketUrl", "process", "env", "REACT_APP_API_URL", "token", "localStorage", "getItem", "current", "auth", "transports", "timeout", "reconnection", "reconnectionDelay", "reconnectionAttempts", "forceNew", "rejectUnauthorized", "secure", "upgrade", "rememberUpgrade", "setupSocketListeners", "getUserMedia", "roomId", "emit", "error", "socket", "on", "reason", "roomSize", "createPeerConnection", "setTimeout", "createOffer", "sdp", "handleOffer", "handleAnswer", "candidate", "handleCandidate", "info", "srcObject", "stream", "navigator", "mediaDevices", "video", "audio", "pc", "RTCPeerConnection", "onicecandidate", "event", "ontrack", "remoteStream", "streams", "onconnectionstatechange", "connectionState", "getTracks", "for<PERSON>ach", "track", "addTrack", "offer", "setLocalDescription", "signalingState", "setRemoteDescription", "RTCSessionDescription", "answer", "createAnswer", "remoteDescription", "addIceCandidate", "toggleAudio", "audioTrack", "getAudioTracks", "enabled", "toggleVideo", "videoTrack", "getVideoTracks", "toggleSpeaker", "muted", "endCall", "_user$role2", "_user$role3", "_user$role4", "success", "off", "disconnect", "close", "stop", "kind", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "autoPlay", "playsInline", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Projeler/kidgarden/burky_root_web/client/src/pages/meeting/MeetingPage.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport {\n  MicrophoneIcon,\n  VideoCameraIcon,\n  PhoneXMarkIcon,\n  SpeakerWaveIcon\n} from '@heroicons/react/24/outline';\nimport {\n  MicrophoneIcon as MicrophoneIconSolid,\n  VideoCameraIcon as VideoCameraIconSolid,\n  SpeakerWaveIcon as SpeakerWaveIconSolid\n} from '@heroicons/react/24/solid';\nimport io from 'socket.io-client';\nimport toast from 'react-hot-toast';\nimport { useAuth } from '../../hooks/useAuth';\n\n/**\n * P2P WebRTC Meeting Sayfası\n */\nconst MeetingPage = () => {\n  const { appointmentId } = useParams();\n  const navigate = useNavigate();\n  const { user, hasPermission } = useAuth();\n\n  // Permission check is now handled by PagePermission component\n  useEffect(() => {\n    console.log('🔍 MeetingPage initialized for appointment:', appointmentId);\n    console.log('👤 User:', user ? { id: user.id, role: user.role?.name } : 'Not logged in');\n  }, [user, appointmentId]);\n  \n  // State\n  const [isConnected, setIsConnected] = useState(false);\n  const [isAudioMuted, setIsAudioMuted] = useState(false);\n  const [isVideoMuted, setIsVideoMuted] = useState(false);\n  const [isSpeakerOn, setIsSpeakerOn] = useState(true);\n  const [connectionStatus, setConnectionStatus] = useState('Bağlanıyor...');\n  const [participantCount, setParticipantCount] = useState(0);\n  const [isInitiator, setIsInitiator] = useState(false);\n  \n  // Refs\n  const localVideoRef = useRef(null);\n  const remoteVideoRef = useRef(null);\n  const socketRef = useRef(null);\n  const peerConnectionRef = useRef(null);\n  const localStreamRef = useRef(null);\n  \n  // WebRTC Configuration\n  const rtcConfig = {\n    iceServers: [\n      { urls: 'stun:stun.l.google.com:19302' },\n      { urls: 'stun:stun1.l.google.com:19302' },\n    ],\n  };\n\n  useEffect(() => {\n    // Sadece user ve appointmentId varsa bağlantı kur\n    if (user?.id && appointmentId) {\n      console.log('🚀 MEETING: Bağlantı başlatılıyor...', { userId: user.id, appointmentId });\n      initializeConnection();\n    }\n\n    return () => {\n      console.log('🧹 MEETING: Component unmount, cleanup çağrılıyor...');\n      cleanup();\n    };\n  }, [user?.id, appointmentId]);\n\n  const initializeConnection = async () => {\n    try {\n      // Socket.IO bağlantısı - diğer sayfalarla tutarlı URL kullan\n      const socketUrl = process.env.REACT_APP_API_URL || 'https://192.168.1.111:5000';\n      console.log('🔌 MEETING: Socket.IO bağlantısı deneniyor:', socketUrl);\n\n      // Token'ı al (authentication için)\n      const token = localStorage.getItem('accessToken');\n\n      socketRef.current = io(socketUrl, {\n        auth: { token },\n        transports: ['websocket', 'polling'],\n        timeout: 10000,\n        reconnection: true,\n        reconnectionDelay: 1000,\n        reconnectionAttempts: 5,\n        forceNew: false, // Mevcut bağlantıyı yeniden kullan\n        rejectUnauthorized: false, // Self-signed sertifika için\n        secure: true,\n        upgrade: true,\n        rememberUpgrade: false\n      });\n      \n      // Socket event listeners\n      setupSocketListeners();\n      \n      // Kullanıcı medyasını al\n      await getUserMedia();\n      \n      // Odaya katıl\n      const roomId = `appointment-${appointmentId}`;\n      socketRef.current.emit('join', roomId);\n      \n    } catch (error) {\n      console.error('Bağlantı hatası:', error);\n      toast.error('Bağlantı kurulamadı');\n      setConnectionStatus('Bağlantı hatası');\n    }\n  };\n\n  const setupSocketListeners = () => {\n    const socket = socketRef.current;\n\n    // Connection events\n    socket.on('connect', () => {\n      console.log('✅ MEETING: Socket.IO bağlantısı kuruldu:', socket.id);\n      setConnectionStatus('Bağlandı, odaya katılıyor...');\n    });\n\n    socket.on('disconnect', (reason) => {\n      console.log('❌ MEETING: Socket.IO bağlantısı kesildi:', reason);\n      setConnectionStatus('Bağlantı kesildi');\n      setIsConnected(false);\n    });\n\n    socket.on('connect_error', (error) => {\n      console.error('🔥 MEETING: Socket.IO bağlantı hatası:', error);\n      setConnectionStatus('Bağlantı hatası');\n    });\n\n    socket.on('joined', ({ roomId, roomSize }) => {\n      console.log(`✅ MEETING: Odaya katıldı: ${roomId}, Katılımcı sayısı: ${roomSize}`);\n      setParticipantCount(roomSize);\n      setConnectionStatus(`Odada ${roomSize} kişi`);\n\n      if (roomSize === 1) {\n        setConnectionStatus('Karşı tarafı bekleniyor...');\n        setIsInitiator(false); // İlk katılan initiator değil\n      } else if (roomSize === 2) {\n        setIsInitiator(true); // İkinci katılan initiator olur\n      }\n    });\n\n    socket.on('ready', () => {\n      console.log('Oda hazır, WebRTC bağlantısı başlatılıyor');\n      setConnectionStatus('Bağlantı kuruluyor...');\n\n      // Sadece initiator peer connection oluşturur ve offer gönderir\n      if (isInitiator) {\n        console.log('🚀 MEETING: Initiator olarak offer gönderiliyor...');\n        createPeerConnection();\n        setTimeout(() => createOffer(), 1000); // Kısa bir gecikme\n      } else {\n        console.log('⏳ MEETING: Offer bekleniyor...');\n        createPeerConnection(); // Peer connection'ı hazırla ama offer gönderme\n      }\n    });\n\n    socket.on('full', (roomId) => {\n      console.log('Oda dolu:', roomId);\n      toast.error('Görüşme odası dolu');\n      setConnectionStatus('Oda dolu');\n    });\n\n    socket.on('offer', async (sdp) => {\n      console.log('Offer alındı');\n      await handleOffer(sdp);\n    });\n\n    socket.on('answer', async (sdp) => {\n      console.log('Answer alındı');\n      await handleAnswer(sdp);\n    });\n\n    socket.on('candidate', async (candidate) => {\n      console.log('ICE candidate alındı');\n      await handleCandidate(candidate);\n    });\n\n    socket.on('leave', () => {\n      console.log('Karşı taraf ayrıldı');\n      toast.info('Karşı taraf görüşmeden ayrıldı');\n      setConnectionStatus('Karşı taraf ayrıldı');\n      setParticipantCount(1);\n      \n      // Remote video'yu temizle\n      if (remoteVideoRef.current) {\n        remoteVideoRef.current.srcObject = null;\n      }\n    });\n  };\n\n  const getUserMedia = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({ \n        video: true, \n        audio: true \n      });\n      \n      localStreamRef.current = stream;\n      if (localVideoRef.current) {\n        localVideoRef.current.srcObject = stream;\n      }\n      \n      console.log('Kullanıcı medyası alındı');\n    } catch (error) {\n      console.error('Medya erişim hatası:', error);\n      toast.error('Kamera ve mikrofon erişimi gerekli');\n    }\n  };\n\n  const createPeerConnection = () => {\n    // Eğer zaten peer connection varsa, yenisini oluşturma\n    if (peerConnectionRef.current) {\n      console.log('⚠️ MEETING: Peer connection zaten mevcut, yenisi oluşturulmuyor');\n      return peerConnectionRef.current;\n    }\n\n    console.log('🔗 MEETING: Yeni peer connection oluşturuluyor...');\n    const pc = new RTCPeerConnection(rtcConfig);\n    peerConnectionRef.current = pc;\n\n    // ICE candidate event\n    pc.onicecandidate = (event) => {\n      if (event.candidate) {\n        socketRef.current.emit('candidate', {\n          roomId: `appointment-${appointmentId}`,\n          candidate: event.candidate\n        });\n      }\n    };\n\n    // Remote stream event\n    pc.ontrack = (event) => {\n      console.log('Remote stream alındı');\n      const [remoteStream] = event.streams;\n      if (remoteVideoRef.current) {\n        remoteVideoRef.current.srcObject = remoteStream;\n      }\n      setIsConnected(true);\n      setConnectionStatus('Bağlantı kuruldu');\n    };\n\n    // Connection state change\n    pc.onconnectionstatechange = () => {\n      console.log('Connection state:', pc.connectionState);\n      if (pc.connectionState === 'connected') {\n        setIsConnected(true);\n        setConnectionStatus('Bağlantı kuruldu');\n      } else if (pc.connectionState === 'disconnected') {\n        setIsConnected(false);\n        setConnectionStatus('Bağlantı kesildi');\n      }\n    };\n\n    // Local stream'i peer connection'a ekle\n    if (localStreamRef.current) {\n      localStreamRef.current.getTracks().forEach(track => {\n        pc.addTrack(track, localStreamRef.current);\n      });\n    }\n  };\n\n  const createOffer = async () => {\n    try {\n      const pc = peerConnectionRef.current;\n      const offer = await pc.createOffer();\n      await pc.setLocalDescription(offer);\n      \n      socketRef.current.emit('offer', {\n        roomId: `appointment-${appointmentId}`,\n        sdp: offer\n      });\n    } catch (error) {\n      console.error('Offer oluşturma hatası:', error);\n    }\n  };\n\n  const handleOffer = async (sdp) => {\n    try {\n      console.log('📥 MEETING: Offer alındı, işleniyor...');\n\n      // Peer connection yoksa oluştur\n      if (!peerConnectionRef.current) {\n        createPeerConnection();\n      }\n\n      const pc = peerConnectionRef.current;\n\n      // State kontrolü\n      if (pc.signalingState !== 'stable' && pc.signalingState !== 'have-local-offer') {\n        console.log(`⚠️ MEETING: Yanlış signaling state: ${pc.signalingState}, offer atlanıyor`);\n        return;\n      }\n\n      console.log('🔄 MEETING: Remote description ayarlanıyor...');\n      await pc.setRemoteDescription(new RTCSessionDescription(sdp));\n\n      console.log('📤 MEETING: Answer oluşturuluyor...');\n      const answer = await pc.createAnswer();\n      await pc.setLocalDescription(answer);\n\n      console.log('📤 MEETING: Answer gönderiliyor...');\n      socketRef.current.emit('answer', {\n        roomId: `appointment-${appointmentId}`,\n        sdp: answer\n      });\n    } catch (error) {\n      console.error('❌ MEETING: Offer işleme hatası:', error);\n    }\n  };\n\n  const handleAnswer = async (sdp) => {\n    try {\n      console.log('📥 MEETING: Answer alındı, işleniyor...');\n      const pc = peerConnectionRef.current;\n\n      if (!pc) {\n        console.error('❌ MEETING: Peer connection bulunamadı');\n        return;\n      }\n\n      // State kontrolü\n      if (pc.signalingState !== 'have-local-offer') {\n        console.log(`⚠️ MEETING: Yanlış signaling state: ${pc.signalingState}, answer atlanıyor`);\n        return;\n      }\n\n      console.log('🔄 MEETING: Remote description (answer) ayarlanıyor...');\n      await pc.setRemoteDescription(new RTCSessionDescription(sdp));\n      console.log('✅ MEETING: Answer başarıyla işlendi');\n    } catch (error) {\n      console.error('❌ MEETING: Answer işleme hatası:', error);\n    }\n  };\n\n  const handleCandidate = async (candidate) => {\n    try {\n      const pc = peerConnectionRef.current;\n      if (pc && pc.remoteDescription) {\n        await pc.addIceCandidate(candidate);\n      }\n    } catch (error) {\n      console.error('ICE candidate işleme hatası:', error);\n    }\n  };\n\n  const toggleAudio = () => {\n    if (localStreamRef.current) {\n      const audioTrack = localStreamRef.current.getAudioTracks()[0];\n      if (audioTrack) {\n        audioTrack.enabled = !audioTrack.enabled;\n        setIsAudioMuted(!audioTrack.enabled);\n      }\n    }\n  };\n\n  const toggleVideo = () => {\n    if (localStreamRef.current) {\n      const videoTrack = localStreamRef.current.getVideoTracks()[0];\n      if (videoTrack) {\n        videoTrack.enabled = !videoTrack.enabled;\n        setIsVideoMuted(!videoTrack.enabled);\n      }\n    }\n  };\n\n  const toggleSpeaker = () => {\n    if (remoteVideoRef.current) {\n      remoteVideoRef.current.muted = isSpeakerOn;\n      setIsSpeakerOn(!isSpeakerOn);\n    }\n  };\n\n  const endCall = () => {\n    cleanup();\n\n    // Kullanıcının rolüne göre doğru sayfaya yönlendir\n    if (user.role?.name === 'Admin') {\n      navigate('/admin/dashboard');\n    } else if (user.role?.name === 'Expert') {\n      navigate('/expert/sessions');\n    } else if (user.role?.name === 'Client') {\n      navigate('/client/sessions');\n    } else {\n      navigate('/');\n    }\n\n    toast.success('Görüşme sonlandırıldı');\n  };\n\n  const cleanup = () => {\n    console.log('🧹 MEETING: Cleanup başlatılıyor...');\n\n    // Socket bağlantısını kapat\n    if (socketRef.current) {\n      console.log('🔌 MEETING: Socket bağlantısı kapatılıyor...');\n      socketRef.current.emit('leave', `appointment-${appointmentId}`);\n      socketRef.current.off(); // Tüm event listener'ları kaldır\n      socketRef.current.disconnect();\n      socketRef.current = null;\n    }\n\n    // Peer connection'ı kapat\n    if (peerConnectionRef.current) {\n      console.log('🔗 MEETING: Peer connection kapatılıyor...');\n      peerConnectionRef.current.onicecandidate = null;\n      peerConnectionRef.current.ontrack = null;\n      peerConnectionRef.current.close();\n      peerConnectionRef.current = null;\n    }\n\n    // Local stream'i durdur\n    if (localStreamRef.current) {\n      console.log('📹 MEETING: Local stream durduruluyor...');\n      localStreamRef.current.getTracks().forEach(track => {\n        track.stop();\n        console.log(`📹 MEETING: Track durduruldu: ${track.kind}`);\n      });\n      localStreamRef.current = null;\n    }\n\n    // Video elementlerini temizle\n    if (localVideoRef.current) {\n      localVideoRef.current.srcObject = null;\n    }\n    if (remoteVideoRef.current) {\n      remoteVideoRef.current.srcObject = null;\n    }\n\n    // State'leri sıfırla\n    setIsConnected(false);\n    setParticipantCount(0);\n    setConnectionStatus('Bağlantı kesildi');\n\n    console.log('✅ MEETING: Cleanup tamamlandı');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 text-white\">\n      {/* Header */}\n      <div className=\"bg-gray-800 px-6 py-4 border-b border-gray-700\">\n        <div className=\"flex justify-between items-center\">\n          <div>\n            <h1 className=\"text-xl font-semibold\">Psikolojik Danışmanlık Seansı</h1>\n            <p className=\"text-sm text-gray-400\">Randevu #{appointmentId}</p>\n          </div>\n          <div className=\"text-right\">\n            <p className=\"text-sm text-gray-400\">{connectionStatus}</p>\n            <p className=\"text-xs text-gray-500\">{participantCount} katılımcı</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Video Container */}\n      <div className=\"flex-1 p-6\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 h-[calc(100vh-200px)]\">\n          {/* Local Video */}\n          <div className=\"relative bg-gray-800 rounded-lg overflow-hidden\">\n            <video\n              ref={localVideoRef}\n              autoPlay\n              playsInline\n              muted\n              className=\"w-full h-full object-cover\"\n            />\n            <div className=\"absolute bottom-4 left-4 bg-black bg-opacity-50 px-3 py-1 rounded\">\n              <span className=\"text-sm\">Siz</span>\n            </div>\n            {isVideoMuted && (\n              <div className=\"absolute inset-0 bg-gray-700 flex items-center justify-center\">\n                <VideoCameraIconSolid className=\"h-16 w-16 text-gray-400\" />\n              </div>\n            )}\n          </div>\n\n          {/* Remote Video */}\n          <div className=\"relative bg-gray-800 rounded-lg overflow-hidden\">\n            <video\n              ref={remoteVideoRef}\n              autoPlay\n              playsInline\n              className=\"w-full h-full object-cover\"\n            />\n            <div className=\"absolute bottom-4 left-4 bg-black bg-opacity-50 px-3 py-1 rounded\">\n              <span className=\"text-sm\">Karşı Taraf</span>\n            </div>\n            {!isConnected && (\n              <div className=\"absolute inset-0 bg-gray-700 flex items-center justify-center\">\n                <div className=\"text-center\">\n                  <VideoCameraIconSolid className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n                  <p className=\"text-gray-400\">Karşı taraf bekleniyor...</p>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Controls */}\n      <div className=\"fixed bottom-6 left-1/2 transform -translate-x-1/2\">\n        <div className=\"flex space-x-4 bg-gray-800 rounded-full px-6 py-3 shadow-lg\">\n          {/* Mikrofon */}\n          <button\n            onClick={toggleAudio}\n            className={`p-3 rounded-full transition-colors ${\n              isAudioMuted \n                ? 'bg-red-600 hover:bg-red-700' \n                : 'bg-gray-600 hover:bg-gray-700'\n            }`}\n          >\n            {isAudioMuted ? (\n              <MicrophoneIconSolid className=\"h-6 w-6\" />\n            ) : (\n              <MicrophoneIcon className=\"h-6 w-6\" />\n            )}\n          </button>\n\n          {/* Kamera */}\n          <button\n            onClick={toggleVideo}\n            className={`p-3 rounded-full transition-colors ${\n              isVideoMuted \n                ? 'bg-red-600 hover:bg-red-700' \n                : 'bg-gray-600 hover:bg-gray-700'\n            }`}\n          >\n            {isVideoMuted ? (\n              <VideoCameraIconSolid className=\"h-6 w-6\" />\n            ) : (\n              <VideoCameraIcon className=\"h-6 w-6\" />\n            )}\n          </button>\n\n          {/* Hoparlör */}\n          <button\n            onClick={toggleSpeaker}\n            className={`p-3 rounded-full transition-colors ${\n              !isSpeakerOn \n                ? 'bg-red-600 hover:bg-red-700' \n                : 'bg-gray-600 hover:bg-gray-700'\n            }`}\n          >\n            {isSpeakerOn ? (\n              <SpeakerWaveIcon className=\"h-6 w-6\" />\n            ) : (\n              <SpeakerWaveIconSolid className=\"h-6 w-6\" />\n            )}\n          </button>\n\n          {/* Görüşmeyi Sonlandır */}\n          <button\n            onClick={endCall}\n            className=\"p-3 rounded-full bg-red-600 hover:bg-red-700 transition-colors\"\n          >\n            <PhoneXMarkIcon className=\"h-6 w-6\" />\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default MeetingPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SACEC,cAAc,EACdC,eAAe,EACfC,cAAc,EACdC,eAAe,QACV,6BAA6B;AACpC,SACEH,cAAc,IAAII,mBAAmB,EACrCH,eAAe,IAAII,oBAAoB,EACvCF,eAAe,IAAIG,oBAAoB,QAClC,2BAA2B;AAClC,OAAOC,EAAE,MAAM,kBAAkB;AACjC,OAAOC,KAAK,MAAM,iBAAiB;AACnC,SAASC,OAAO,QAAQ,qBAAqB;;AAE7C;AACA;AACA;AAFA,SAAAC,MAAA,IAAAC,OAAA;AAGA,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC;EAAc,CAAC,GAAGhB,SAAS,CAAC,CAAC;EACrC,MAAMiB,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEiB,IAAI;IAAEC;EAAc,CAAC,GAAGR,OAAO,CAAC,CAAC;;EAEzC;EACAb,SAAS,CAAC,MAAM;IAAA,IAAAsB,UAAA;IACdC,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEN,aAAa,CAAC;IACzEK,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEJ,IAAI,GAAG;MAAEK,EAAE,EAAEL,IAAI,CAACK,EAAE;MAAEC,IAAI,GAAAJ,UAAA,GAAEF,IAAI,CAACM,IAAI,cAAAJ,UAAA,uBAATA,UAAA,CAAWK;IAAK,CAAC,GAAG,eAAe,CAAC;EAC1F,CAAC,EAAE,CAACP,IAAI,EAAEF,aAAa,CAAC,CAAC;;EAEzB;EACA,MAAM,CAACU,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACqC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtC,QAAQ,CAAC,eAAe,CAAC;EACzE,MAAM,CAACuC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxC,QAAQ,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACyC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAM2C,aAAa,GAAGzC,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM0C,cAAc,GAAG1C,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM2C,SAAS,GAAG3C,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM4C,iBAAiB,GAAG5C,MAAM,CAAC,IAAI,CAAC;EACtC,MAAM6C,cAAc,GAAG7C,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAM8C,SAAS,GAAG;IAChBC,UAAU,EAAE,CACV;MAAEC,IAAI,EAAE;IAA+B,CAAC,EACxC;MAAEA,IAAI,EAAE;IAAgC,CAAC;EAE7C,CAAC;EAEDjD,SAAS,CAAC,MAAM;IACd;IACA,IAAIoB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEK,EAAE,IAAIP,aAAa,EAAE;MAC7BK,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE;QAAE0B,MAAM,EAAE9B,IAAI,CAACK,EAAE;QAAEP;MAAc,CAAC,CAAC;MACvFiC,oBAAoB,CAAC,CAAC;IACxB;IAEA,OAAO,MAAM;MACX5B,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;MACnE4B,OAAO,CAAC,CAAC;IACX,CAAC;EACH,CAAC,EAAE,CAAChC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,EAAE,EAAEP,aAAa,CAAC,CAAC;EAE7B,MAAMiC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF;MACA,MAAME,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,4BAA4B;MAC/EjC,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE6B,SAAS,CAAC;;MAErE;MACA,MAAMI,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MAEjDf,SAAS,CAACgB,OAAO,GAAGjD,EAAE,CAAC0C,SAAS,EAAE;QAChCQ,IAAI,EAAE;UAAEJ;QAAM,CAAC;QACfK,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;QACpCC,OAAO,EAAE,KAAK;QACdC,YAAY,EAAE,IAAI;QAClBC,iBAAiB,EAAE,IAAI;QACvBC,oBAAoB,EAAE,CAAC;QACvBC,QAAQ,EAAE,KAAK;QAAE;QACjBC,kBAAkB,EAAE,KAAK;QAAE;QAC3BC,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE,IAAI;QACbC,eAAe,EAAE;MACnB,CAAC,CAAC;;MAEF;MACAC,oBAAoB,CAAC,CAAC;;MAEtB;MACA,MAAMC,YAAY,CAAC,CAAC;;MAEpB;MACA,MAAMC,MAAM,GAAG,eAAexD,aAAa,EAAE;MAC7C0B,SAAS,CAACgB,OAAO,CAACe,IAAI,CAAC,MAAM,EAAED,MAAM,CAAC;IAExC,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdrD,OAAO,CAACqD,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxChE,KAAK,CAACgE,KAAK,CAAC,qBAAqB,CAAC;MAClCvC,mBAAmB,CAAC,iBAAiB,CAAC;IACxC;EACF,CAAC;EAED,MAAMmC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMK,MAAM,GAAGjC,SAAS,CAACgB,OAAO;;IAEhC;IACAiB,MAAM,CAACC,EAAE,CAAC,SAAS,EAAE,MAAM;MACzBvD,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEqD,MAAM,CAACpD,EAAE,CAAC;MAClEY,mBAAmB,CAAC,8BAA8B,CAAC;IACrD,CAAC,CAAC;IAEFwC,MAAM,CAACC,EAAE,CAAC,YAAY,EAAGC,MAAM,IAAK;MAClCxD,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEuD,MAAM,CAAC;MAC/D1C,mBAAmB,CAAC,kBAAkB,CAAC;MACvCR,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,CAAC;IAEFgD,MAAM,CAACC,EAAE,CAAC,eAAe,EAAGF,KAAK,IAAK;MACpCrD,OAAO,CAACqD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9DvC,mBAAmB,CAAC,iBAAiB,CAAC;IACxC,CAAC,CAAC;IAEFwC,MAAM,CAACC,EAAE,CAAC,QAAQ,EAAE,CAAC;MAAEJ,MAAM;MAAEM;IAAS,CAAC,KAAK;MAC5CzD,OAAO,CAACC,GAAG,CAAC,6BAA6BkD,MAAM,uBAAuBM,QAAQ,EAAE,CAAC;MACjFzC,mBAAmB,CAACyC,QAAQ,CAAC;MAC7B3C,mBAAmB,CAAC,SAAS2C,QAAQ,OAAO,CAAC;MAE7C,IAAIA,QAAQ,KAAK,CAAC,EAAE;QAClB3C,mBAAmB,CAAC,4BAA4B,CAAC;QACjDI,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;MACzB,CAAC,MAAM,IAAIuC,QAAQ,KAAK,CAAC,EAAE;QACzBvC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;MACxB;IACF,CAAC,CAAC;IAEFoC,MAAM,CAACC,EAAE,CAAC,OAAO,EAAE,MAAM;MACvBvD,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxDa,mBAAmB,CAAC,uBAAuB,CAAC;;MAE5C;MACA,IAAIG,WAAW,EAAE;QACfjB,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;QACjEyD,oBAAoB,CAAC,CAAC;QACtBC,UAAU,CAAC,MAAMC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;MACzC,CAAC,MAAM;QACL5D,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC7CyD,oBAAoB,CAAC,CAAC,CAAC,CAAC;MAC1B;IACF,CAAC,CAAC;IAEFJ,MAAM,CAACC,EAAE,CAAC,MAAM,EAAGJ,MAAM,IAAK;MAC5BnD,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEkD,MAAM,CAAC;MAChC9D,KAAK,CAACgE,KAAK,CAAC,oBAAoB,CAAC;MACjCvC,mBAAmB,CAAC,UAAU,CAAC;IACjC,CAAC,CAAC;IAEFwC,MAAM,CAACC,EAAE,CAAC,OAAO,EAAE,MAAOM,GAAG,IAAK;MAChC7D,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;MAC3B,MAAM6D,WAAW,CAACD,GAAG,CAAC;IACxB,CAAC,CAAC;IAEFP,MAAM,CAACC,EAAE,CAAC,QAAQ,EAAE,MAAOM,GAAG,IAAK;MACjC7D,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B,MAAM8D,YAAY,CAACF,GAAG,CAAC;IACzB,CAAC,CAAC;IAEFP,MAAM,CAACC,EAAE,CAAC,WAAW,EAAE,MAAOS,SAAS,IAAK;MAC1ChE,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnC,MAAMgE,eAAe,CAACD,SAAS,CAAC;IAClC,CAAC,CAAC;IAEFV,MAAM,CAACC,EAAE,CAAC,OAAO,EAAE,MAAM;MACvBvD,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MAClCZ,KAAK,CAAC6E,IAAI,CAAC,gCAAgC,CAAC;MAC5CpD,mBAAmB,CAAC,qBAAqB,CAAC;MAC1CE,mBAAmB,CAAC,CAAC,CAAC;;MAEtB;MACA,IAAII,cAAc,CAACiB,OAAO,EAAE;QAC1BjB,cAAc,CAACiB,OAAO,CAAC8B,SAAS,GAAG,IAAI;MACzC;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMjB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMkB,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACpB,YAAY,CAAC;QACvDqB,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE;MACT,CAAC,CAAC;MAEFjD,cAAc,CAACc,OAAO,GAAG+B,MAAM;MAC/B,IAAIjD,aAAa,CAACkB,OAAO,EAAE;QACzBlB,aAAa,CAACkB,OAAO,CAAC8B,SAAS,GAAGC,MAAM;MAC1C;MAEApE,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACzC,CAAC,CAAC,OAAOoD,KAAK,EAAE;MACdrD,OAAO,CAACqD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5ChE,KAAK,CAACgE,KAAK,CAAC,oCAAoC,CAAC;IACnD;EACF,CAAC;EAED,MAAMK,oBAAoB,GAAGA,CAAA,KAAM;IACjC;IACA,IAAIpC,iBAAiB,CAACe,OAAO,EAAE;MAC7BrC,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;MAC9E,OAAOqB,iBAAiB,CAACe,OAAO;IAClC;IAEArC,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAChE,MAAMwE,EAAE,GAAG,IAAIC,iBAAiB,CAAClD,SAAS,CAAC;IAC3CF,iBAAiB,CAACe,OAAO,GAAGoC,EAAE;;IAE9B;IACAA,EAAE,CAACE,cAAc,GAAIC,KAAK,IAAK;MAC7B,IAAIA,KAAK,CAACZ,SAAS,EAAE;QACnB3C,SAAS,CAACgB,OAAO,CAACe,IAAI,CAAC,WAAW,EAAE;UAClCD,MAAM,EAAE,eAAexD,aAAa,EAAE;UACtCqE,SAAS,EAAEY,KAAK,CAACZ;QACnB,CAAC,CAAC;MACJ;IACF,CAAC;;IAED;IACAS,EAAE,CAACI,OAAO,GAAID,KAAK,IAAK;MACtB5E,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnC,MAAM,CAAC6E,YAAY,CAAC,GAAGF,KAAK,CAACG,OAAO;MACpC,IAAI3D,cAAc,CAACiB,OAAO,EAAE;QAC1BjB,cAAc,CAACiB,OAAO,CAAC8B,SAAS,GAAGW,YAAY;MACjD;MACAxE,cAAc,CAAC,IAAI,CAAC;MACpBQ,mBAAmB,CAAC,kBAAkB,CAAC;IACzC,CAAC;;IAED;IACA2D,EAAE,CAACO,uBAAuB,GAAG,MAAM;MACjChF,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEwE,EAAE,CAACQ,eAAe,CAAC;MACpD,IAAIR,EAAE,CAACQ,eAAe,KAAK,WAAW,EAAE;QACtC3E,cAAc,CAAC,IAAI,CAAC;QACpBQ,mBAAmB,CAAC,kBAAkB,CAAC;MACzC,CAAC,MAAM,IAAI2D,EAAE,CAACQ,eAAe,KAAK,cAAc,EAAE;QAChD3E,cAAc,CAAC,KAAK,CAAC;QACrBQ,mBAAmB,CAAC,kBAAkB,CAAC;MACzC;IACF,CAAC;;IAED;IACA,IAAIS,cAAc,CAACc,OAAO,EAAE;MAC1Bd,cAAc,CAACc,OAAO,CAAC6C,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAI;QAClDX,EAAE,CAACY,QAAQ,CAACD,KAAK,EAAE7D,cAAc,CAACc,OAAO,CAAC;MAC5C,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMuB,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMa,EAAE,GAAGnD,iBAAiB,CAACe,OAAO;MACpC,MAAMiD,KAAK,GAAG,MAAMb,EAAE,CAACb,WAAW,CAAC,CAAC;MACpC,MAAMa,EAAE,CAACc,mBAAmB,CAACD,KAAK,CAAC;MAEnCjE,SAAS,CAACgB,OAAO,CAACe,IAAI,CAAC,OAAO,EAAE;QAC9BD,MAAM,EAAE,eAAexD,aAAa,EAAE;QACtCkE,GAAG,EAAEyB;MACP,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACdrD,OAAO,CAACqD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAMS,WAAW,GAAG,MAAOD,GAAG,IAAK;IACjC,IAAI;MACF7D,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;MAErD;MACA,IAAI,CAACqB,iBAAiB,CAACe,OAAO,EAAE;QAC9BqB,oBAAoB,CAAC,CAAC;MACxB;MAEA,MAAMe,EAAE,GAAGnD,iBAAiB,CAACe,OAAO;;MAEpC;MACA,IAAIoC,EAAE,CAACe,cAAc,KAAK,QAAQ,IAAIf,EAAE,CAACe,cAAc,KAAK,kBAAkB,EAAE;QAC9ExF,OAAO,CAACC,GAAG,CAAC,uCAAuCwE,EAAE,CAACe,cAAc,mBAAmB,CAAC;QACxF;MACF;MAEAxF,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5D,MAAMwE,EAAE,CAACgB,oBAAoB,CAAC,IAAIC,qBAAqB,CAAC7B,GAAG,CAAC,CAAC;MAE7D7D,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClD,MAAM0F,MAAM,GAAG,MAAMlB,EAAE,CAACmB,YAAY,CAAC,CAAC;MACtC,MAAMnB,EAAE,CAACc,mBAAmB,CAACI,MAAM,CAAC;MAEpC3F,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjDoB,SAAS,CAACgB,OAAO,CAACe,IAAI,CAAC,QAAQ,EAAE;QAC/BD,MAAM,EAAE,eAAexD,aAAa,EAAE;QACtCkE,GAAG,EAAE8B;MACP,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACdrD,OAAO,CAACqD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD;EACF,CAAC;EAED,MAAMU,YAAY,GAAG,MAAOF,GAAG,IAAK;IAClC,IAAI;MACF7D,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD,MAAMwE,EAAE,GAAGnD,iBAAiB,CAACe,OAAO;MAEpC,IAAI,CAACoC,EAAE,EAAE;QACPzE,OAAO,CAACqD,KAAK,CAAC,uCAAuC,CAAC;QACtD;MACF;;MAEA;MACA,IAAIoB,EAAE,CAACe,cAAc,KAAK,kBAAkB,EAAE;QAC5CxF,OAAO,CAACC,GAAG,CAAC,uCAAuCwE,EAAE,CAACe,cAAc,oBAAoB,CAAC;QACzF;MACF;MAEAxF,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;MACrE,MAAMwE,EAAE,CAACgB,oBAAoB,CAAC,IAAIC,qBAAqB,CAAC7B,GAAG,CAAC,CAAC;MAC7D7D,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IACpD,CAAC,CAAC,OAAOoD,KAAK,EAAE;MACdrD,OAAO,CAACqD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAC1D;EACF,CAAC;EAED,MAAMY,eAAe,GAAG,MAAOD,SAAS,IAAK;IAC3C,IAAI;MACF,MAAMS,EAAE,GAAGnD,iBAAiB,CAACe,OAAO;MACpC,IAAIoC,EAAE,IAAIA,EAAE,CAACoB,iBAAiB,EAAE;QAC9B,MAAMpB,EAAE,CAACqB,eAAe,CAAC9B,SAAS,CAAC;MACrC;IACF,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdrD,OAAO,CAACqD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED,MAAM0C,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIxE,cAAc,CAACc,OAAO,EAAE;MAC1B,MAAM2D,UAAU,GAAGzE,cAAc,CAACc,OAAO,CAAC4D,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7D,IAAID,UAAU,EAAE;QACdA,UAAU,CAACE,OAAO,GAAG,CAACF,UAAU,CAACE,OAAO;QACxC1F,eAAe,CAAC,CAACwF,UAAU,CAACE,OAAO,CAAC;MACtC;IACF;EACF,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI5E,cAAc,CAACc,OAAO,EAAE;MAC1B,MAAM+D,UAAU,GAAG7E,cAAc,CAACc,OAAO,CAACgE,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7D,IAAID,UAAU,EAAE;QACdA,UAAU,CAACF,OAAO,GAAG,CAACE,UAAU,CAACF,OAAO;QACxCxF,eAAe,CAAC,CAAC0F,UAAU,CAACF,OAAO,CAAC;MACtC;IACF;EACF,CAAC;EAED,MAAMI,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIlF,cAAc,CAACiB,OAAO,EAAE;MAC1BjB,cAAc,CAACiB,OAAO,CAACkE,KAAK,GAAG5F,WAAW;MAC1CC,cAAc,CAAC,CAACD,WAAW,CAAC;IAC9B;EACF,CAAC;EAED,MAAM6F,OAAO,GAAGA,CAAA,KAAM;IAAA,IAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA;IACpB9E,OAAO,CAAC,CAAC;;IAET;IACA,IAAI,EAAA4E,WAAA,GAAA5G,IAAI,CAACM,IAAI,cAAAsG,WAAA,uBAATA,WAAA,CAAWrG,IAAI,MAAK,OAAO,EAAE;MAC/BR,QAAQ,CAAC,kBAAkB,CAAC;IAC9B,CAAC,MAAM,IAAI,EAAA8G,WAAA,GAAA7G,IAAI,CAACM,IAAI,cAAAuG,WAAA,uBAATA,WAAA,CAAWtG,IAAI,MAAK,QAAQ,EAAE;MACvCR,QAAQ,CAAC,kBAAkB,CAAC;IAC9B,CAAC,MAAM,IAAI,EAAA+G,WAAA,GAAA9G,IAAI,CAACM,IAAI,cAAAwG,WAAA,uBAATA,WAAA,CAAWvG,IAAI,MAAK,QAAQ,EAAE;MACvCR,QAAQ,CAAC,kBAAkB,CAAC;IAC9B,CAAC,MAAM;MACLA,QAAQ,CAAC,GAAG,CAAC;IACf;IAEAP,KAAK,CAACuH,OAAO,CAAC,uBAAuB,CAAC;EACxC,CAAC;EAED,MAAM/E,OAAO,GAAGA,CAAA,KAAM;IACpB7B,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;;IAElD;IACA,IAAIoB,SAAS,CAACgB,OAAO,EAAE;MACrBrC,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAC3DoB,SAAS,CAACgB,OAAO,CAACe,IAAI,CAAC,OAAO,EAAE,eAAezD,aAAa,EAAE,CAAC;MAC/D0B,SAAS,CAACgB,OAAO,CAACwE,GAAG,CAAC,CAAC,CAAC,CAAC;MACzBxF,SAAS,CAACgB,OAAO,CAACyE,UAAU,CAAC,CAAC;MAC9BzF,SAAS,CAACgB,OAAO,GAAG,IAAI;IAC1B;;IAEA;IACA,IAAIf,iBAAiB,CAACe,OAAO,EAAE;MAC7BrC,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;MACzDqB,iBAAiB,CAACe,OAAO,CAACsC,cAAc,GAAG,IAAI;MAC/CrD,iBAAiB,CAACe,OAAO,CAACwC,OAAO,GAAG,IAAI;MACxCvD,iBAAiB,CAACe,OAAO,CAAC0E,KAAK,CAAC,CAAC;MACjCzF,iBAAiB,CAACe,OAAO,GAAG,IAAI;IAClC;;IAEA;IACA,IAAId,cAAc,CAACc,OAAO,EAAE;MAC1BrC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACvDsB,cAAc,CAACc,OAAO,CAAC6C,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAI;QAClDA,KAAK,CAAC4B,IAAI,CAAC,CAAC;QACZhH,OAAO,CAACC,GAAG,CAAC,iCAAiCmF,KAAK,CAAC6B,IAAI,EAAE,CAAC;MAC5D,CAAC,CAAC;MACF1F,cAAc,CAACc,OAAO,GAAG,IAAI;IAC/B;;IAEA;IACA,IAAIlB,aAAa,CAACkB,OAAO,EAAE;MACzBlB,aAAa,CAACkB,OAAO,CAAC8B,SAAS,GAAG,IAAI;IACxC;IACA,IAAI/C,cAAc,CAACiB,OAAO,EAAE;MAC1BjB,cAAc,CAACiB,OAAO,CAAC8B,SAAS,GAAG,IAAI;IACzC;;IAEA;IACA7D,cAAc,CAAC,KAAK,CAAC;IACrBU,mBAAmB,CAAC,CAAC,CAAC;IACtBF,mBAAmB,CAAC,kBAAkB,CAAC;IAEvCd,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;EAC9C,CAAC;EAED,oBACET,OAAA;IAAK0H,SAAS,EAAC,qCAAqC;IAAAC,QAAA,gBAElD3H,OAAA;MAAK0H,SAAS,EAAC,gDAAgD;MAAAC,QAAA,eAC7D3H,OAAA;QAAK0H,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChD3H,OAAA;UAAA2H,QAAA,gBACE3H,OAAA;YAAI0H,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxE/H,OAAA;YAAG0H,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,WAAS,EAACxH,aAAa;UAAA;YAAAyH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACN/H,OAAA;UAAK0H,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB3H,OAAA;YAAG0H,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAEtG;UAAgB;YAAAuG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3D/H,OAAA;YAAG0H,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAEpG,gBAAgB,EAAC,2BAAU;UAAA;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/H,OAAA;MAAK0H,SAAS,EAAC,YAAY;MAAAC,QAAA,eACzB3H,OAAA;QAAK0H,SAAS,EAAC,6DAA6D;QAAAC,QAAA,gBAE1E3H,OAAA;UAAK0H,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAC9D3H,OAAA;YACEgI,GAAG,EAAErG,aAAc;YACnBsG,QAAQ;YACRC,WAAW;YACXnB,KAAK;YACLW,SAAS,EAAC;UAA4B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACF/H,OAAA;YAAK0H,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChF3H,OAAA;cAAM0H,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,EACL9G,YAAY,iBACXjB,OAAA;YAAK0H,SAAS,EAAC,+DAA+D;YAAAC,QAAA,eAC5E3H,OAAA,CAACN,oBAAoB;cAACgI,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN/H,OAAA;UAAK0H,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAC9D3H,OAAA;YACEgI,GAAG,EAAEpG,cAAe;YACpBqG,QAAQ;YACRC,WAAW;YACXR,SAAS,EAAC;UAA4B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACF/H,OAAA;YAAK0H,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChF3H,OAAA;cAAM0H,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,EACL,CAAClH,WAAW,iBACXb,OAAA;YAAK0H,SAAS,EAAC,+DAA+D;YAAAC,QAAA,eAC5E3H,OAAA;cAAK0H,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B3H,OAAA,CAACN,oBAAoB;gBAACgI,SAAS,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzE/H,OAAA;gBAAG0H,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/H,OAAA;MAAK0H,SAAS,EAAC,oDAAoD;MAAAC,QAAA,eACjE3H,OAAA;QAAK0H,SAAS,EAAC,6DAA6D;QAAAC,QAAA,gBAE1E3H,OAAA;UACEmI,OAAO,EAAE5B,WAAY;UACrBmB,SAAS,EAAE,sCACT3G,YAAY,GACR,6BAA6B,GAC7B,+BAA+B,EAClC;UAAA4G,QAAA,EAEF5G,YAAY,gBACXf,OAAA,CAACP,mBAAmB;YAACiI,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAE3C/H,OAAA,CAACX,cAAc;YAACqI,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACtC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAGT/H,OAAA;UACEmI,OAAO,EAAExB,WAAY;UACrBe,SAAS,EAAE,sCACTzG,YAAY,GACR,6BAA6B,GAC7B,+BAA+B,EAClC;UAAA0G,QAAA,EAEF1G,YAAY,gBACXjB,OAAA,CAACN,oBAAoB;YAACgI,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAE5C/H,OAAA,CAACV,eAAe;YAACoI,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACvC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAGT/H,OAAA;UACEmI,OAAO,EAAErB,aAAc;UACvBY,SAAS,EAAE,sCACT,CAACvG,WAAW,GACR,6BAA6B,GAC7B,+BAA+B,EAClC;UAAAwG,QAAA,EAEFxG,WAAW,gBACVnB,OAAA,CAACR,eAAe;YAACkI,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEvC/H,OAAA,CAACL,oBAAoB;YAAC+H,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAC5C;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAGT/H,OAAA;UACEmI,OAAO,EAAEnB,OAAQ;UACjBU,SAAS,EAAC,gEAAgE;UAAAC,QAAA,eAE1E3H,OAAA,CAACT,cAAc;YAACmI,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7H,EAAA,CA3hBID,WAAW;EAAA,QACWd,SAAS,EAClBC,WAAW,EACIU,OAAO;AAAA;AAAAsI,EAAA,GAHnCnI,WAAW;AA6hBjB,eAAeA,WAAW;AAAC,IAAAmI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}